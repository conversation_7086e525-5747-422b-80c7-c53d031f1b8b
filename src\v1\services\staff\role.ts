import { db } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import { formatString } from '../../utils/stringFormatter';
import { staffHasPermission, PERMISSIONS } from '../../utils/permission';
import { deleteCacheByPattern } from '../../utils/cache';
import { logger } from '../../utils/logger';

// Helper function to clear all admin-related caches
export const clearStaffCaches = async (): Promise<void> => {
  await deleteCacheByPattern('staff:*');
};

export const roleService = {
  getAllRoles: async (staffId: any, query: any) => {
    try {
    await staffHasPermission(staffId, PERMISSIONS.ROLE_VIEW);
    return db.role.findMany({
      include: {
        permissions: true,
      },
    });
    } catch (error) {
      logger.error('Error fetching roles:', error);
      throw new HttpError('Failed to fetch roles', 400);
    }
    
  },

  createRole: async (staffId: any, reqBody: any) => {
    try {
    await staffHasPermission(staffId, PERMISSIONS.ROLE_CREATE);
    const { name, description, permissionIds } = reqBody;
    const formattedString = formatString.trimString(name);

    const checkRoleExist = await db.role.findUnique({
      where: { name: formattedString },
    });

    if (checkRoleExist) {
      throw new HttpError('Role with the name already exists', 400);
    }

    await db.role.create({
      data: {
        name,
        description,
        permissions: {
          connect: permissionIds.map((id: number) => ({ id })),
        },
      },
    });

    clearStaffCaches();

    return {
      message: 'Role created successfully',
    };
    } catch (error) {
      logger.error('Error creating role:', error);
      throw new HttpError('Failed to create role', 400);
    }
  },

  updateRole: async (staffId: any, reqBody: any) => {
    try {
    await staffHasPermission(staffId, PERMISSIONS.ROLE_EDIT);
    const { roleId, description, permissionIds } = reqBody;

    // Build update object dynamically
    const updateData: any = {};

    if (description !== undefined) updateData.description = description;
    if (permissionIds !== undefined) {
      updateData.permissions = {
        set: permissionIds.map((id: number) => ({ id })),
      };
    }

    const role = await db.role.update({
      where: { id: roleId },
      data: updateData,
    });
    clearStaffCaches();

    return {
      message: `${role.name} - Role updated successfully`,
    };
    } catch (error) {
      logger.error('Error updating role:', error);
      throw new HttpError('Failed to update role', 400);
    }

  },

  getAllPermission: async (staffId: any, query: any) => {
    try {
    await staffHasPermission(staffId, PERMISSIONS.PERMISSION_VIEW);
    return db.permission.findMany();
    } catch (error) {
      logger.error('Error fetching permissions:', error);   
      throw new HttpError('Failed to fetch permissions', 400);
    }
  },
  createPermission: async (staffId: any, reqBody: any) => {
    try {
    await staffHasPermission(staffId, PERMISSIONS.ROLE_CREATE);
    const { action, description } = reqBody;
    const formattedString = formatString.trimString(action);

    const checkExist = await db.permission.findUnique({
      where: { action: formattedString },
    });

    if (checkExist) {
      throw new HttpError('Permission already exists', 400);
    }

    await db.permission.create({
      data: {
        action,
        description,
      },
    });

    return {
      message: 'Role created successfully',
    };
  } catch (error) {
    logger.error('Error creating permission:', error);
      throw new HttpError('Failed to create permission', 400);
    }
  },
};
