import { db } from './model';
import {
  getCache,
  setCache,
  deleteCache,
  deleteCacheByPattern,
} from '../utils/cache';
import { HttpError } from './httpError';

export const PERMISSIONS = {
  PACKAGE_VIEW: 'package:view',
  PACKAGE_CREATE: 'package:create',
  PACKAGE_EDIT: 'package:edit',
  PACKAGE_DELETE: 'package:delete',

  BOOKING_VIEW: 'booking:view',
  BOOKING_UPDATE: 'booking:update',

  LOCATION_CREATE: 'location:create',
  LOCATION_EDIT: 'location:edit',
  LOCATION_DELETE: 'location:delete',
  LOCATION_ALL: 'location:all',
  LOCATION_REGION: 'location:region',

  INCIDENT_CREATE: 'incident:create',
  INCIDENT_VIEW: 'incident:view',
  INCIDENT_EDIT: 'incident:edit',
  INCIDENT_SUBMIT: 'incident:submit',
  INCIDENT_APPROVE: 'incident:approve',
  INCIDENT_DELETE: 'incident:delete',
  INCIDENT_COMMENT: 'incident:comment',
  INCIDENT_ASSIGN: 'incident:assign',
  INCIDENT_CLOSE: 'incident:close',

  REWARD_VIEW: 'reward:view',
  REWARD_CREATE: 'reward:create',
  REWARD_EDIT: 'reward:edit',
  REWARD_DELETE: 'reward:delete',

  FEEDBACK_VIEW: 'feedback:view',

  REFERRAL_VIEW: 'referral:view',
  REFERRAL_CREATE: 'referral:create',
  REFERRAL_EDIT: 'referral:edit',
  REFERRAL_DELETE: 'referral:delete',

  TRANSACTION_VIEW: 'transaction:view',
  TRANSACTION_CREATE: 'transaction:create',
  TRANSACTION_EDIT: 'transaction:edit',

  STAFF_CREATE: 'staff:create',
  STAFF_VIEW: 'staff:view',
  STAFF_EDIT: 'staff:edit',
  STAFF_EDIT_OWN: 'staff:edit_own',
  STAFF_DELETE: 'staff:delete',

  SETTINGS_VIEW: 'settings:view',
  SETTINGS_CREATE: 'settings:create',
  SETTINGS_EDIT: 'settings:edit',
  SETTINGS_DELETE: 'settings:delete',

  REQUISITION_CREATE: 'requisition:create',
  REQUISITION_VIEW: 'requisition:view',
  REQUISITION_EDIT: 'requisition:edit',
  REQUISITION_SUBMIT: 'requisition:submit',
  REQUISITION_APPROVE: 'requisition:approve',
  REQUISITION_DELETE: 'requisition:delete',
  REQUISITION_CANCEL: 'requisition:cancel',

  FORUM_VIEW: 'forum:view',
  FORUM_CREATE_GROUP: 'forum:create_group',
  FORUM_VIEW_GROUP: 'forum:view_group',
  FORUM_EDIT_GROUP: 'forum:edit_group',
  FORUM_DELETE_GROUP: 'forum:delete_group',
  FORUM_ADD_MEMBERS: 'forum:add_members',
  FORUM_REMOVE_MEMBERS: 'forum:remove_members',
  FORUM_JOIN_GROUP: 'forum:join_group',
  FORUM_CREATE_CHANNEL: 'forum:create_channel',
  FORUM_EDIT_CHANNEL: 'forum:edit_channel',
  FORUM_DELETE_CHANNEL: 'forum:delete_channel',
  FORUM_SEND_MESSAGE: 'forum:send_message',
  FORUM_VIEW_MESSAGES: 'forum:view_messages',
  FORUM_EDIT_MESSAGE: 'forum:edit_message',
  FORUM_DELETE_MESSAGE: 'forum:delete_message',
  FORUM_MODERATE_MESSAGES: 'forum:moderate_messages',
  FORUM_PIN_MESSAGE: 'forum:pin_message',
  FORUM_DIRECT_MESSAGE: 'forum:direct_message',

  PATIENT_VIEW: 'patient:view',
  PATIENT_CREATE: 'patient:create',
  PATIENT_EDIT: 'patient:edit',
  PATIENT_DELETE: 'patient:delete',

  ROLE_VIEW: 'role:view',
  ROLE_CREATE: 'role:create',
  ROLE_EDIT: 'role:edit',
  ROLE_DELETE: 'role:delete',

  PERMISSION_VIEW: 'permission:view',
  PERMISSION_CREATE: 'permission:create',
  PERMISSION_EDIT: 'permission:edit',
  PERMISSION_DELETE: 'permission:delete',

  INTERACTION_VIEW: 'interaction:view',
  INTERACTION_CREATE: 'interaction:create',
  INTERACTION_EDIT: 'interaction:edit',
  INTERACTION_REPLY: 'interaction:reply',

  CAFETERIA_VIEW: 'cafeteria:view',
  CAFETERIA_INVENTORY_MANAGE: 'cafeteria:inventory_manage',
  CAFETERIA_MENU_MANAGE: 'cafeteria:menu_manage',
  CAFETERIA_ORDERS_MANAGE: 'cafeteria:orders_manage',
  CAFETERIA_POS_ACCESS: 'cafeteria:pos_access',
  CAFETERIA_SPECIAL_APPROVE: 'cafeteria:special_approve',

  // Game permissions
  GAME_VIEW: 'game:view',
  GAME_CREATE: 'game:create',
  GAME_EDIT: 'game:edit',
  GAME_DELETE: 'game:delete',
  GAME_PARTICIPATE: 'game:participate',
  GAME_MANAGE: 'game:manage',
  GAME_VIEW_ANALYTICS: 'game:view_analytics',

  CHIS_VIEW: 'chis:view',
  CHIS_CREATE: 'chis:create',
  CHIS_EDIT: 'chis:edit',
  CHIS_APPROVE: 'chis:approve',
  CHIS_GENERATE_CODE: 'chis:generate_code',

  HUB_VIEW: 'hub:view',
  HUB_CREATE: 'hub:create',
  HUB_EDIT: 'hub:edit',

  PROCESS_VIEW: 'process:view',
  PROCESS_CREATE: 'process:create',
  PROCESS_EDIT: 'process:edit',

  AI_ASSISTANT_VIEW: 'ai_assistant:view',
  AI_ASSISTANT_CHAT: 'ai_assistant:chat',
};

export async function staffHasPermission(
  staffId: number,
  permission: string
): Promise<boolean> {
  const result = await db.staff.findFirst({
    where: {
      id: Number(staffId),
      roles: {
        some: {
          permissions: {
            some: {
              action: permission,
            },
          },
        },
      },
    },
  });

  if (result) return true;
  throw new HttpError('You are not authorized to perform this action', 403);
}

type StaffPermissionsCache = {
  locationId: number | null;
  regionId: number | null;
  permissions: string[];
};

export function createStaffAuthHelper(staffId: number) {
  let inMemoryCache: StaffPermissionsCache | null = null;

  const cacheKey = `staff:permissions:${staffId}`;

  // Load from Redis or fallback to DB
  async function loadAllPermissions(): Promise<void> {
    if (inMemoryCache) return;

    // Try Redis
    const redisData = await getCache(cacheKey);
    if (redisData) {
      const parsed = JSON.parse(redisData as string) as StaffPermissionsCache;
      inMemoryCache = {
        permissions: parsed.permissions,
        locationId: parsed.locationId,
        regionId: parsed.regionId,
      };
      return;
    }

    // Fallback to DB
    const staff = await db.staff.findUnique({
      where: { id: Number(staffId) },
      select: {
        locationId: true,
        location: {
          select: {
            regionId: true,
          },
        },
        roles: {
          select: {
            permissions: {
              select: { action: true },
            },
          },
        },
      },
    });

    if (!staff) {
      throw new HttpError('Staff not found', 404);
    }

    const permissions = staff.roles.flatMap((role) =>
      role.permissions
        .map((p) => p.action)
        .filter((action): action is string => action !== null)
    );

    inMemoryCache = {
      locationId: staff.locationId,
      regionId: staff.location?.regionId || null,
      permissions,
    };

    // Cache it in Redis
    await setCache(cacheKey, JSON.stringify(inMemoryCache), 60 * 5);
  }

  // Single permission check
  async function hasPermission(permission: string): Promise<boolean> {
    await loadAllPermissions();
    return inMemoryCache!.permissions.includes(permission);
  }

  // Multi-permission check
  async function hasPermissions(required: string[]): Promise<boolean> {
    await loadAllPermissions();
    return required.every((p) => inMemoryCache!.permissions.includes(p));
  }

  // Get locationId
  async function getLocationId(): Promise<number | null> {
    await loadAllPermissions();
    return inMemoryCache!.locationId;
  }

  // Get regionId
  async function getRegionId(): Promise<number | null> {
    await loadAllPermissions();
    return inMemoryCache!.regionId;
  }

  return {
    hasPermission,
    hasPermissions,
    getLocationId,
    getRegionId,
    loadAllPermissions,
  };
}
