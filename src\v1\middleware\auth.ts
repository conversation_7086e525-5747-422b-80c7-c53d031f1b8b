import { Request, Response, NextFunction } from 'express';
import jwt, { Secret, JwtPayload } from 'jsonwebtoken';
import config from '../../config/app.config';

export const SECRET_KEY: Secret =
  config.SIGNING_TOKEN_SECRET as unknown as string;

export interface CustomRequest extends JwtPayload {
  userId: string | number;
}

export const adminAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    if (!token) {
      throw new Error();
    }
    const decoded = jwt.verify(token, SECRET_KEY) as CustomRequest;
    const adminId = decoded.id;
    req.Admin = adminId;
    next();
  } catch (error) {
    res.status(401).json({
      succes: false,
      message: 'Please authenticate the administrator',
    });
  }
};

export const secure = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    if (!token) {
      throw new Error();
    }
    const decoded = jwt.verify(token, SECRET_KEY) as CustomRequest;
    const staffId = decoded.id;
    req.Staff = staffId;
    next();
  } catch (error) {
    res.status(401).json({
      succes: false,
      message: 'Please authenticate the staff',
    });
  }
};

export const referrerAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    if (!token) {
      throw new Error();
    }
    const decoded = jwt.verify(token, SECRET_KEY) as CustomRequest;
    const referrerId = decoded.id;
    req.Referrer = referrerId;
    next();
  } catch (error) {
    res.status(401).json({
      succes: false,
      message: 'Please authenticate the the referrer',
    });
  }
};
