import { Request, Response, NextFunction } from 'express';
import { controllerOperations } from '../handlers/handleController';
import { packageBookingService } from '../../services/booking/package';

const PackageBookingHandler = (req: Request, res: Response) => {
  controllerOperations(
    packageBookingService.bookPackage,
    undefined,
    res,
    req.body
  );
};

const ListAllPackageBookingHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    packageBookingService.getAllPackageBookings,
    req.query,
    res,
    staffId
  );
};

const CompletePackageBookingHandler = (req: Request, res: Response) => {
  controllerOperations(
    packageBookingService.completePackageBooking,
    undefined,
    res,
    req.body
  );
};

const SinglePackageHandler = (req: Request, res: Response) => {
  const { bookingId } = req.params;
  controllerOperations(
    packageBookingService.getPackageBookingById,
    undefined,
    res,
    bookingId
  );
};

const DeletePackageBookingHandler = (req: Request, res: Response) => {
  const { id } = req.params;
  controllerOperations(packageBookingService.deleteBooking, undefined, res, id);
};

const UpdatePackageBookingHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    packageBookingService.updateBooking,
    req.body,
    res,
    staffId
  );
};

export const bookingControllers = {
  PackageBookingHandler,
  CompletePackageBookingHandler,
  SinglePackageHandler,
  DeletePackageBookingHandler,
  ListAllPackageBookingHandler,
  UpdatePackageBookingHandler,
};
