import { Request, Response, NextFunction } from 'express';
import { controllerOperations } from '../handlers/handleController';
import { PackageTestService } from '../../services/package/test';

const ListPackageTestHandler = (req: Request, res: Response) => {
  controllerOperations(PackageTestService.getAllPackageTest, undefined, res);
};

const ListInvestigationAdminHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    PackageTestService.getAllInvestigationAdmin,
    req.query,
    res,
    staffId
  );
};

const CreateInvestigationHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    PackageTestService.createInvestigation,
    req.body,
    res,
    staffId
  );
};

const DeleteInvestigationHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  const id = Number(req.params.id);

  controllerOperations(
    PackageTestService.deleteInvestigation,
    id,
    res,
    staffId
  );
};

export const PackageTestControllers = {
  ListPackageTestHandler,
  ListInvestigationAdminHandler,
  CreateInvestigationHandler,
  DeleteInvestigationHandler,
};
