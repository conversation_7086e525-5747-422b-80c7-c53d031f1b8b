import { db } from '../../utils/model';
import { logger } from '../../utils/logger';
import { sendToGame, sendToUser } from '../socket';

export interface JoinGameData {
  gameId: string;
  staffId: number;
}

export interface SubmitAnswerData {
  participantId: number;
  questionId: string;
  answer: string;
  timeSpent?: number;
}

export class GameParticipationService {
  // Join a game
  static async joinGame(data: JoinGameData) {
    try {
      // Check if game exists and is available for joining
      const game = await db.game.findFirst({
        where: {
          id: data.gameId,
          status: { in: ['PUBLISHED', 'ACTIVE'] },
        },
        include: {
          _count: {
            select: {
              participants: true,
            },
          },
        },
      });

      if (!game) {
        throw new Error('Game not found or not available for joining');
      }

      // Check if game has started
      if (new Date() > game.endDate) {
        throw new Error('Game has already ended');
      }

      // Check if user is already a participant
      const existingParticipant = await db.gameParticipant.findUnique({
        where: {
          gameId_staffId: {
            gameId: data.gameId,
            staffId: data.staffId,
          },
        },
      });

      if (existingParticipant) {
        throw new Error('You are already participating in this game');
      }

      // Check if game has reached max participants
      if (
        game.maxParticipants &&
        game._count.participants >= game.maxParticipants
      ) {
        throw new Error('Game has reached maximum number of participants');
      }

      // Check if user has sufficient balance for entry fee
      if (game.entryFee && Number(game.entryFee) > 0) {
        const staff = await db.staff.findUnique({
          where: { id: data.staffId },
          select: { wallet: true },
        });

        if (!staff || Number(staff.wallet) < Number(game.entryFee)) {
          throw new Error('Insufficient wallet balance for entry fee');
        }

        // Deduct entry fee
        await db.staff.update({
          where: { id: data.staffId },
          data: {
            wallet: {
              decrement: game.entryFee,
            },
          },
        });

        // Create transaction record
        await db.transaction.create({
          data: {
            amount: game.entryFee,
            type: 'REWARD',
            status: 'SUCCESS',
            reference: `GAME_ENTRY_${game.id}_${data.staffId}_${Date.now()}`,
            mode: 'WALLET',
            remarks: `Game entry fee - ${game.title}`,
            staffId: data.staffId,
            locationId: game.locationId,
          },
        });
      }

      // Create participant record
      const participant = await db.gameParticipant.create({
        data: {
          gameId: data.gameId,
          staffId: data.staffId,
        },
        include: {
          staff: {
            select: {
              id: true,
              fullName: true,
            },
          },
          game: {
            select: {
              id: true,
              title: true,
            },
          },
        },
      });

      // Notify game participants about new player
      sendToGame(data.gameId, 'player_joined', {
        gameId: data.gameId,
        participant,
        joinedAt: new Date(),
      });

      logger.info(`User ${data.staffId} joined game ${data.gameId}`);
      return participant;
    } catch (error) {
      logger.error('Error joining game:', error);
      throw error;
    }
  }

  // Leave a game (before it starts)
  static async leaveGame(gameId: string, staffId: number) {
    try {
      const participant = await db.gameParticipant.findFirst({
        where: {
          gameId,
          staffId,
          status: 'ACTIVE',
        },
        include: {
          game: true,
        },
      });

      if (!participant) {
        throw new Error('You are not participating in this game');
      }

      // Check if game has already started
      if (participant.game.status === 'ACTIVE') {
        throw new Error('Cannot leave game after it has started');
      }

      // Update participant status
      await db.gameParticipant.update({
        where: { id: participant.id },
        data: { status: 'WITHDRAWN' },
      });

      // Refund entry fee if applicable
      if (participant.game.entryFee && Number(participant.game.entryFee) > 0) {
        await db.staff.update({
          where: { id: staffId },
          data: {
            wallet: {
              increment: participant.game.entryFee,
            },
          },
        });

        // Create refund transaction record
        await db.transaction.create({
          data: {
            amount: participant.game.entryFee,
            type: 'REFUND',
            status: 'SUCCESS',
            reference: `GAME_REFUND_${participant.gameId}_${staffId}_${Date.now()}`,
            mode: 'WALLET',
            remarks: `Game entry fee refund - ${participant.game.title}`,
            staffId,
            locationId: participant.game.locationId,
          },
        });
      }

      // Notify game participants
      sendToGame(gameId, 'player_left', {
        gameId,
        staffId,
        leftAt: new Date(),
      });

      logger.info(`User ${staffId} left game ${gameId}`);
      return { success: true };
    } catch (error) {
      logger.error('Error leaving game:', error);
      throw error;
    }
  }

  // Get participant's game progress
  static async getParticipantProgress(gameId: string, staffId: number) {
    try {
      const participant = await db.gameParticipant.findFirst({
        where: {
          gameId,
          staffId,
        },
        include: {
          game: {
            include: {
              questions: {
                orderBy: { order: 'asc' },
                select: {
                  id: true,
                  question: true,
                  questionType: true,
                  options: true,
                  points: true,
                  timeLimit: true,
                  order: true,
                },
              },
            },
          },
          answers: {
            include: {
              question: {
                select: {
                  id: true,
                  order: true,
                },
              },
            },
          },
        },
      });

      if (!participant) {
        throw new Error('You are not participating in this game');
      }

      // Calculate progress
      const totalQuestions = participant.game.questions.length;
      const answeredQuestions = participant.answers.length;
      const progressPercentage =
        totalQuestions > 0 ? (answeredQuestions / totalQuestions) * 100 : 0;

      return {
        participant: {
          id: participant.id,
          score: participant.score,
          status: participant.status,
          joinedAt: participant.joinedAt,
          completedAt: participant.completedAt,
        },
        game: {
          id: participant.game.id,
          title: participant.game.title,
          status: participant.game.status,
          questions: participant.game.questions,
        },
        progress: {
          totalQuestions,
          answeredQuestions,
          progressPercentage,
          remainingQuestions: totalQuestions - answeredQuestions,
        },
        answers: participant.answers,
      };
    } catch (error) {
      logger.error('Error getting participant progress:', error);
      throw error;
    }
  }

  // Submit answer to a question
  static async submitAnswer(data: SubmitAnswerData) {
    try {
      // Get participant and question details
      const participant = await db.gameParticipant.findUnique({
        where: { id: data.participantId },
        include: {
          game: {
            include: {
              questions: true,
            },
          },
        },
      });

      if (!participant) {
        throw new Error('Participant not found');
      }

      if (participant.status !== 'ACTIVE') {
        throw new Error('Participant is not active in the game');
      }

      if (participant.game.status !== 'ACTIVE') {
        throw new Error('Game is not active');
      }

      // Check if question exists in this game
      const question = participant.game.questions.find(
        (q) => q.id === data.questionId
      );
      if (!question) {
        throw new Error('Question not found in this game');
      }

      // Check if answer already exists
      const existingAnswer = await db.gameAnswer.findUnique({
        where: {
          participantId_questionId: {
            participantId: data.participantId,
            questionId: data.questionId,
          },
        },
      });

      if (existingAnswer) {
        throw new Error('Answer already submitted for this question');
      }

      // Evaluate answer
      let isCorrect = false;
      let pointsEarned = 0;

      if (question.correctAnswer) {
        // Simple string comparison for now - can be enhanced for different question types
        isCorrect =
          data.answer.toLowerCase().trim() ===
          question.correctAnswer.toLowerCase().trim();
        if (isCorrect) {
          pointsEarned = question.points;
        }
      }

      // Create answer record
      const answer = await db.gameAnswer.create({
        data: {
          participantId: data.participantId,
          questionId: data.questionId,
          answer: data.answer,
          isCorrect,
          pointsEarned,
          timeSpent: data.timeSpent,
        },
        include: {
          question: {
            select: {
              id: true,
              question: true,
              points: true,
            },
          },
        },
      });

      // Update participant score
      await db.gameParticipant.update({
        where: { id: data.participantId },
        data: {
          score: {
            increment: pointsEarned,
          },
        },
      });

      // Check if participant has answered all questions
      const totalAnswers = await db.gameAnswer.count({
        where: { participantId: data.participantId },
      });

      if (totalAnswers === participant.game.questions.length) {
        // Mark participant as completed
        await db.gameParticipant.update({
          where: { id: data.participantId },
          data: {
            status: 'COMPLETED',
            completedAt: new Date(),
          },
        });

        // Notify about completion
        sendToGame(participant.gameId, 'player_completed', {
          gameId: participant.gameId,
          participantId: data.participantId,
          staffId: participant.staffId,
          completedAt: new Date(),
        });
      }

      // Notify about answer submission
      sendToUser(participant.staffId, 'answer_submitted', {
        gameId: participant.gameId,
        questionId: data.questionId,
        isCorrect,
        pointsEarned,
        totalScore: participant.score + pointsEarned,
      });

      logger.info(
        `Answer submitted: participant ${data.participantId}, question ${data.questionId}, correct: ${isCorrect}`
      );
      return {
        answer,
        isCorrect,
        pointsEarned,
        totalScore: participant.score + pointsEarned,
      };
    } catch (error) {
      logger.error('Error submitting answer:', error);
      throw error;
    }
  }

  // Get game leaderboard
  static async getGameLeaderboard(gameId: string, limit = 10) {
    try {
      const leaderboard = await db.gameLeaderboard.findMany({
        where: { gameId },
        include: {
          staff: {
            select: {
              id: true,
              fullName: true,
            },
          },
        },
        orderBy: { position: 'asc' },
        take: limit,
      });

      return leaderboard;
    } catch (error) {
      logger.error('Error getting game leaderboard:', error);
      throw error;
    }
  }

  // Get participant's answers for a game
  static async getParticipantAnswers(gameId: string, staffId: number) {
    try {
      const participant = await db.gameParticipant.findFirst({
        where: {
          gameId,
          staffId,
        },
      });

      if (!participant) {
        throw new Error('You are not participating in this game');
      }

      const answers = await db.gameAnswer.findMany({
        where: { participantId: participant.id },
        include: {
          question: {
            select: {
              id: true,
              question: true,
              questionType: true,
              points: true,
              order: true,
            },
          },
        },
        orderBy: {
          question: {
            order: 'asc',
          },
        },
      });

      return answers;
    } catch (error) {
      logger.error('Error getting participant answers:', error);
      throw error;
    }
  }
}
