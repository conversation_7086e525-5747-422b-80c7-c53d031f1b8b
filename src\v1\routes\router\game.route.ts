import { Router } from 'express';
import { gameRoute } from './game/game.route';
import { gameParticipationRoute } from './game/participation.route';
import { gameAnalyticsRoute } from './game/analytics.route';

export const gameMainRoute = Router();

// Game management routes
gameMainRoute.use('/', gameRoute);

// Game participation routes
gameMainRoute.use('/participation', gameParticipationRoute);

// Game analytics routes
gameMainRoute.use('/analytics', gameAnalyticsRoute);
