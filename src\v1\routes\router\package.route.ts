import { Router } from 'express';
import { secure } from '../../middleware/auth';
import { uploads } from '../../utils/image/uploadImage';
import { categoryControllers } from '../../controllers/package/category.controller';
import { packageControllers } from '../../controllers/package/package.controller';
import { locationPriceControllers } from '../../controllers/package/locationPrice.controller';
import { PackageTestControllers } from '../../controllers/package/test.controller';

export const packageRoute = Router();

//Category routes
packageRoute.post(
  '/create-category',
  secure,
  categoryControllers.CreateCategoryHandler
);
packageRoute.get(
  '/list-category',
  secure,
  categoryControllers.ListPackageCategoryHandler
);
packageRoute.get(
  '/list-public-category',
  categoryControllers.ListPublicCategoryHandler
);
packageRoute.get(
  '/get-category/:slug',
  categoryControllers.SinglePackageCategoryHandler
);
packageRoute.get(
  '/get-category-id/:categoryId',
  categoryControllers.SinglePackageCategoryByIdHandler
);
packageRoute.patch(
  '/update-category',
  secure,
  categoryControllers.UpdatePackageCategoryHandler
);
packageRoute.delete(
  '/delete-category/:categoryId',
  secure,
  categoryControllers.DeletePackageCategoryHandler
);

// Package routes
packageRoute.post(
  '/add-new-package',
  secure,
  uploads.single('image'),
  packageControllers.CreatePackageHandler
);
packageRoute.get('/list-package', packageControllers.ListPackageHandler);
packageRoute.get(
  '/list-package-admin',
  secure,
  packageControllers.ListAllPackageForAdmin
);
packageRoute.get(
  '/single-package/:slug',
  packageControllers.SinglePackageHandler
);
packageRoute.get(
  '/single-package-admin/:slug',
  secure,
  packageControllers.SinglePackageAdminHandler
);
packageRoute.get(
  '/list-package-link',
  packageControllers.ListAllPackageForLinkHandler
);
packageRoute.patch(
  '/update-package',
  secure,
  packageControllers.UpdatePackageHandler
);
packageRoute.patch(
  '/update-package-status',
  secure,
  packageControllers.UpdatePackageStatusHandler
);

packageRoute.patch(
  '/update-package-image',
  secure,
  uploads.single('image'),
  packageControllers.UpdatePackageImageHandler
);

// Package Location Price routes
packageRoute.get(
  '/list-location-price',
  locationPriceControllers.ListLocationPriceHandler
);
packageRoute.patch(
  '/update-package-prices',
  secure,
  locationPriceControllers.UpdatePackagePriceHandler
);

// Test routes

packageRoute.get(
  '/list-test',
  secure,
  PackageTestControllers.ListPackageTestHandler
);

packageRoute.get(
  '/list-investigation-paginated',
  secure,
  PackageTestControllers.ListInvestigationAdminHandler
);

packageRoute.post(
  '/add-investigation',
  secure,
  PackageTestControllers.CreateInvestigationHandler
);

packageRoute.delete(
  '/delete-investigation/:id',
  secure,
  PackageTestControllers.DeleteInvestigationHandler
);
