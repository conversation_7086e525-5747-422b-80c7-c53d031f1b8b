import { Request, Response } from 'express';
import { controllerOperations } from '../handlers/handleController';
import { innovationIdeaService } from '../../services/innovation/innovationIdea';
import { innovationIdeaLikeService } from '../../services/innovation/innovationIdeaLike';
import { innovationIdeaCommentService } from '../../services/innovation/innovationIdeaComment';

// Innovation Idea Controllers
const CreateInnovationIdeaHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    innovationIdeaService.createInnovationIdea,
    req.body,
    res,
    staffId
  );
};

const GetAllInnovationIdeasHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    innovationIdeaService.getAllInnovationIdeas,
    req.query,
    res,
    staffId
  );
};

const UpdateInnovationIdeaHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  const { ideaId } = req.params;
  controllerOperations(
    innovationIdeaService.updateInnovationIdea,
    parseInt(ideaId),
    res,
    staffId,
    req.body
  );
};

const DeleteInnovationIdeaHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  const { ideaId } = req.params;
  controllerOperations(
    innovationIdeaService.deleteInnovationIdea,
    parseInt(ideaId),
    res,
    staffId
  );
};

// Innovation Idea Like Controllers
const ToggleLikeHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  const { ideaId } = req.params;
  controllerOperations(
    innovationIdeaLikeService.toggleLike,
    parseInt(ideaId),
    res,
    staffId
  );
};

const CreateCommentHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    innovationIdeaCommentService.createComment,
    req.body,
    res,
    staffId
  );
};

const DeleteCommentHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  const { commentId } = req.params;
  controllerOperations(
    innovationIdeaCommentService.deleteComment,
    parseInt(commentId),
    res,
    staffId
  );
};

export const innovationIdeaControllers = {
  CreateInnovationIdeaHandler,
  GetAllInnovationIdeasHandler,
  UpdateInnovationIdeaHandler,
  DeleteInnovationIdeaHandler,
  ToggleLikeHandler,
  CreateCommentHandler,
  DeleteCommentHandler,
};
