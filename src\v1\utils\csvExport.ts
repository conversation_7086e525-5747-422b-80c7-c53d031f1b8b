import { formatDate } from './util';

/**
 * Interface for CSV export options
 */
export interface CSVExportOptions {
  filename?: string;
  headers?: string[];
  dateFields?: string[];
  numberFields?: string[];
  excludeFields?: string[];
}

/**
 * Converts an array of objects to CSV format
 * @param data - Array of objects to convert
 * @param options - CSV export options
 * @returns CSV string
 */
export const convertToCSV = (
  data: any[],
  options: CSVExportOptions = {}
): string => {
  if (!data || data.length === 0) {
    return '';
  }

  const {
    headers,
    dateFields = [],
    numberFields = [],
    excludeFields = [],
  } = options;

  // Get all unique keys from the data, excluding specified fields
  const allKeys = Array.from(
    new Set(
      data.flatMap((item) =>
        Object.keys(flattenObject(item)).filter(
          (key) => !excludeFields.includes(key)
        )
      )
    )
  );

  // Use provided headers or generate from keys
  const csvHeaders = headers || allKeys;

  // Create header row
  const headerRow = csvHeaders
    .map((header) => escapeCSVField(header))
    .join(',');

  // Create data rows
  const dataRows = data.map((item) => {
    const flatItem = flattenObject(item);
    return csvHeaders
      .map((header) => {
        let value = flatItem[header];

        // Handle null/undefined values
        if (value === null || value === undefined) {
          return '';
        }

        // Format date fields
        if (dateFields.includes(header) && value) {
          if (value instanceof Date) {
            value = formatDate(value, true);
          } else if (typeof value === 'string') {
            const date = new Date(value);
            if (!isNaN(date.getTime())) {
              value = formatDate(date, true);
            }
          }
        }

        // Format number fields
        if (numberFields.includes(header) && typeof value === 'number') {
          value = value.toLocaleString();
        }

        return escapeCSVField(String(value));
      })
      .join(',');
  });

  return [headerRow, ...dataRows].join('\n');
};

/**
 * Flattens nested objects for CSV export
 * @param obj - Object to flatten
 * @param prefix - Prefix for nested keys
 * @returns Flattened object
 */
const flattenObject = (obj: any, prefix: string = ''): any => {
  const flattened: any = {};

  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const value = obj[key];
      const newKey = prefix ? `${prefix}.${key}` : key;

      if (
        value !== null &&
        typeof value === 'object' &&
        !Array.isArray(value) &&
        !(value instanceof Date)
      ) {
        // Recursively flatten nested objects
        Object.assign(flattened, flattenObject(value, newKey));
      } else if (Array.isArray(value)) {
        // Convert arrays to comma-separated strings
        flattened[newKey] = value
          .map((item) =>
            typeof item === 'object' ? JSON.stringify(item) : String(item)
          )
          .join('; ');
      } else {
        flattened[newKey] = value;
      }
    }
  }

  return flattened;
};

/**
 * Escapes CSV field values to handle commas, quotes, and newlines
 * @param field - Field value to escape
 * @returns Escaped field value
 */
const escapeCSVField = (field: string): string => {
  if (
    field.includes(',') ||
    field.includes('"') ||
    field.includes('\n') ||
    field.includes('\r')
  ) {
    // Escape quotes by doubling them and wrap the entire field in quotes
    return `"${field.replace(/"/g, '""')}"`;
  }
  return field;
};

/**
 * Generates a filename for CSV export
 * @param baseName - Base name for the file
 * @param includeTimestamp - Whether to include timestamp in filename
 * @returns Generated filename
 */
export const generateCSVFilename = (
  baseName: string,
  includeTimestamp: boolean = true
): string => {
  const sanitizedBaseName = baseName.replace(/[^a-zA-Z0-9_-]/g, '_');

  if (includeTimestamp) {
    const now = new Date();
    const timestamp = now.toISOString().replace(/[:.]/g, '-').slice(0, 19);
    return `${sanitizedBaseName}_${timestamp}.csv`;
  }

  return `${sanitizedBaseName}.csv`;
};

/**
 * Creates CSV export response data
 * @param data - Data to export
 * @param filename - Filename for the export
 * @param options - CSV export options
 * @returns Object with CSV content and metadata
 */
export const createCSVExport = (
  data: any[],
  filename: string,
  options: CSVExportOptions = {}
) => {
  const csvContent = convertToCSV(data, options);
  const csvFilename = generateCSVFilename(filename);

  return {
    content: csvContent,
    filename: csvFilename,
    contentType: 'text/csv',
    headers: {
      'Content-Type': 'text/csv; charset=utf-8',
      'Content-Disposition': `attachment; filename="${csvFilename}"`,
    },
  };
};

/**
 * Common CSV export configurations for different data types
 */
export const CSV_CONFIGS = {
  orders: {
    dateFields: ['createdAt', 'updatedAt'],
    numberFields: ['totalAmount', 'quantity', 'unitPrice', 'totalPrice'],
    excludeFields: ['id', 'staffId', 'locationId'],
  },
  orderItems: {
    dateFields: ['createdAt', 'updatedAt'],
    numberFields: ['quantity', 'unitPrice', 'totalPrice'],
    excludeFields: ['id', 'orderId', 'menuItemId'],
  },
  staff: {
    dateFields: ['createdAt', 'updatedAt', 'lastLogin'],
    numberFields: ['wallet', 'creditLimit', 'monthlyCreditUsed'],
    excludeFields: ['password', 'id'],
  },
};
