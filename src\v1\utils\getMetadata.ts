// utils/getRequestMetadata.ts

import { IncomingMessage } from 'http';
import { UAParser } from 'ua-parser-js';
import { devLog } from './logger';

export type RequestMetadata = {
  ip: string;
  userAgent: string;
  device: {
    browser: string;
    os: string;
    device: string;
    deviceType: string;
    deviceVendor: string;
  };
  url: string;
  acceptLanguage: string;
  referer: string;
  staffId?: string; // Optional, only if the user is authenticated
};

export function getRequestMetadata(req: IncomingMessage): RequestMetadata {
  // Get IP address
  const forwarded = req.headers['x-forwarded-for'];
  const ip =
    typeof forwarded === 'string'
      ? forwarded.split(',')[0].trim()
      : req.socket.remoteAddress || '';

  // Handle ::1 (IPv6 loopback)
  const clientIp = ip === '::1' ? '127.0.0.1' : ip;

  const userAgent = req.headers['user-agent'] || '';
  const result = new UAParser(userAgent).getResult();
  devLog('User Agent:', userAgent);

  const device = {
    browser: result.browser.name
      ? `${result.browser.name} ${result.browser.version}`
      : 'Unknown',
    os: result.os.name ? `${result.os.name} ${result.os.version}` : 'Unknown',
    device: result.device.model || 'Desktop',
    deviceType: result.device.type || 'Unknown',
    deviceVendor: result.device.vendor || 'Unknown',
  };

  // Extract headers and other useful info
  const acceptLanguage = req.headers['accept-language'] || 'Unknown';
  const referer = req.headers['referer'] || 'Unknown';
  const url = req.url || 'Unknown';
  const staffId = (req as any).Staff;

  return {
    ip: clientIp,
    staffId,
    userAgent,
    device,
    acceptLanguage,
    referer,
    url,
  };
}
