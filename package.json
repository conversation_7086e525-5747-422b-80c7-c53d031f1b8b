{"name": "backend", "version": "1.0.0", "main": "index.js", "repository": "https://github.com/cedarcrest-hospital/backend.git", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "UNLICENSED", "scripts": {"build": "npx prisma migrate deploy && prisma generate && tsc && cpx \"./src/v1/services/emails/**/*\" ./dist/v1/services/emails --clean", "start": "cross-env NODE_ENV=production node ./dist/src/server.js", "seed": "prisma db seed", "dev": "cross-env NODE_ENV=development nodemon --files src/server.ts", "lint": "eslint . --ext .ts --fix", "format": "prettier --write .", "migrate:deploy": "npx prisma migrate deploy"}, "dependencies": {"@bull-board/api": "^6.9.2", "@bull-board/express": "^6.9.2", "@prisma/client": "^6.14.0", "@socket.io/redis-adapter": "^8.2.1", "axios": "^1.7.9", "bcryptjs": "^3.0.2", "bull": "^4.12.2", "cloudinary": "^2.5.1", "cloudinary-build-url": "^0.2.4", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dayjs": "^1.11.13", "dotenv": "^16.4.7", "express": "^4.21.2", "express-handlebars": "^7.1.2", "helmet": "^8.0.0", "ioredis": "^5.3.2", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.8", "nodemailer-express-handlebars": "^6.1.0", "socket.io": "^4.8.1", "ua-parser-js": "^2.0.3", "uuid": "^11.1.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "prisma": {"seed": "ts-node prisma/seed.ts"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/bull": "^4.10.0", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/ioredis": "^5.0.0", "@types/jsonwebtoken": "^9.0.9", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.12", "@types/node": "^22.13.5", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.15", "@types/nodemailer-express-handlebars": "^4.0.5", "@types/socket.io": "^3.0.2", "cpx": "^1.5.0", "cross-env": "^7.0.3", "nodemon": "^3.1.9", "prettier": "^3.5.2", "prisma": "^6.14.0", "ts-node": "^10.9.2", "typescript": "^5.7.3"}}