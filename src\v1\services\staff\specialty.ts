import { db } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import { formatString } from '../../utils/stringFormatter';
import { staffHasPermission, PERMISSIONS } from '../../utils/permission';
import { logger } from '../../utils/logger';

export const specialtyService = {
  getAllSpecialty: async (staffId: any, query: any) => {
    try {
    return db.specialty.findMany();
    } catch (error) {
      logger.error('Error fetching specialties:', error);
    throw new HttpError('Failed to fetch specialties', 400);
  }
  },

  createSpecialty: async (staffId: any, reqBody: any) => {
    try {
    await staffHasPermission(staffId, PERMISSIONS.STAFF_CREATE);
    const { name } = reqBody;
    const formattedString = formatString.trimString(name);

    const checkSpecialty = await db.specialty.findFirst({
      where: { name: formattedString },
    });

    if (checkSpecialty) {
      throw new HttpError('Specialty already exists', 400);
    }
    await db.specialty.create({
      data: {
        name: formattedString,
      },
    });
    return {
      message: 'Specialty created successfully',
    };
    } catch (error) {
      logger.error('Error creating specialty:', error);
    throw new HttpError('Failed to create specialty', 400);
  }
  },
};
