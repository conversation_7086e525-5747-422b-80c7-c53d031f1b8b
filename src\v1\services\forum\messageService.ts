import { PrismaClient, MessageType } from '@prisma/client';
import { logger } from '../../utils/logger';
import {
  sendToChannel,
  notifyUserMention,
  notifyForumMessage,
} from '../socket';
import { ForumNotificationService } from '../notification/forumNotificationService';

const prisma = new PrismaClient();

export interface CreateMessageData {
  content: string;
  messageType?: MessageType;
  channelId: number;
  authorId: number;
  parentMessageId?: string;
  attachments?: {
    fileName: string;
    fileUrl: string;
    fileType: string;
    fileSize: number;
  }[];
  mentions?: number[];
}

export interface UpdateMessageData {
  content?: string;
  isEdited?: boolean;
}

export class ForumMessageService {
  // Create a new message
  static async createMessage(data: CreateMessageData) {
    try {
      // Check if user has access to the channel
      const channel = await prisma.forumChannel.findFirst({
        where: {
          id: data.channelId,
          isActive: true,
          group: {
            isActive: true,
            OR: [
              { isPrivate: false },
              {
                isPrivate: true,
                members: {
                  some: {
                    staffId: data.authorId,
                    isActive: true,
                  },
                },
              },
            ],
          },
        },
        include: {
          group: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      if (!channel) {
        throw new Error('Channel not found or access denied');
      }

      // Create the message
      const message = await prisma.forumMessage.create({
        data: {
          content: data.content,
          messageType: data.messageType || 'TEXT',
          channelId: data.channelId,
          authorId: data.authorId,
          parentMessageId: data.parentMessageId,
          attachments: data.attachments
            ? {
                create: data.attachments,
              }
            : undefined,
          mentions: data.mentions
            ? {
                create: data.mentions.map((mentionedStaffId) => ({
                  mentionedStaffId,
                })),
              }
            : undefined,
        },
        include: {
          author: {
            select: {
              id: true,
              fullName: true,
              email: true,
            },
          },
          attachments: true,
          mentions: {
            include: {
              mentionedStaff: {
                select: {
                  id: true,
                  fullName: true,
                },
              },
            },
          },
          parentMessage: {
            include: {
              author: {
                select: {
                  id: true,
                  fullName: true,
                },
              },
            },
          },
          reactions: {
            include: {
              staff: {
                select: {
                  id: true,
                  fullName: true,
                },
              },
            },
          },
        },
      });

      // Send comprehensive notifications using the notification service
      await ForumNotificationService.notifyNewMessage(
        data.channelId,
        message.id,
        data.authorId,
        data.content,
        data.mentions
      );

      // Emit socket event for real-time updates
      notifyForumMessage(data.channelId, message);

      logger.info(
        `Message created: ${message.id} in channel ${data.channelId} by user ${data.authorId}`
      );
      return message;
    } catch (error) {
      logger.error('Error creating message:', error);
      throw error;
    }
  }

  // Get messages in a channel
  static async getChannelMessages(
    channelId: number,
    userId: number,
    page = 1,
    limit = 50
  ) {
    try {
      // Check if user has access to the channel
      const channel = await prisma.forumChannel.findFirst({
        where: {
          id: channelId,
          isActive: true,
          group: {
            isActive: true,
            OR: [
              { isPrivate: false },
              {
                isPrivate: true,
                members: {
                  some: {
                    staffId: userId,
                    isActive: true,
                  },
                },
              },
            ],
          },
        },
      });

      if (!channel) {
        throw new Error('Channel not found or access denied');
      }

      const skip = (page - 1) * limit;

      const [messages, total] = await Promise.all([
        prisma.forumMessage.findMany({
          where: {
            channelId,
            isDeleted: false,
          },
          include: {
            author: {
              select: {
                id: true,
                fullName: true,
                email: true,
              },
            },
            attachments: true,
            mentions: {
              include: {
                mentionedStaff: {
                  select: {
                    id: true,
                    fullName: true,
                  },
                },
              },
            },
            parentMessage: {
              include: {
                author: {
                  select: {
                    id: true,
                    fullName: true,
                  },
                },
              },
            },
            reactions: {
              include: {
                staff: {
                  select: {
                    id: true,
                    fullName: true,
                  },
                },
              },
            },
            _count: {
              select: {
                replies: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit,
        }),
        prisma.forumMessage.count({
          where: {
            channelId,
            isDeleted: false,
          },
        }),
      ]);

      return {
        messages: messages.reverse(), // Reverse to show oldest first
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      logger.error('Error getting channel messages:', error);
      throw error;
    }
  }

  // Get message by ID
  static async getMessageById(messageId: string, userId: number) {
    try {
      const message = await prisma.forumMessage.findFirst({
        where: {
          id: messageId,
          isDeleted: false,
          channel: {
            isActive: true,
            group: {
              isActive: true,
              OR: [
                { isPrivate: false },
                {
                  isPrivate: true,
                  members: {
                    some: {
                      staffId: userId,
                      isActive: true,
                    },
                  },
                },
              ],
            },
          },
        },
        include: {
          author: {
            select: {
              id: true,
              fullName: true,
              email: true,
            },
          },
          channel: {
            select: {
              id: true,
              name: true,
              group: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
          attachments: true,
          mentions: {
            include: {
              mentionedStaff: {
                select: {
                  id: true,
                  fullName: true,
                },
              },
            },
          },
          parentMessage: {
            include: {
              author: {
                select: {
                  id: true,
                  fullName: true,
                },
              },
            },
          },
          replies: {
            include: {
              author: {
                select: {
                  id: true,
                  fullName: true,
                },
              },
              reactions: {
                include: {
                  staff: {
                    select: {
                      id: true,
                      fullName: true,
                    },
                  },
                },
              },
            },
            orderBy: { createdAt: 'asc' },
          },
          reactions: {
            include: {
              staff: {
                select: {
                  id: true,
                  fullName: true,
                },
              },
            },
          },
        },
      });

      if (!message) {
        throw new Error('Message not found or access denied');
      }

      return message;
    } catch (error) {
      logger.error('Error getting message:', error);
      throw error;
    }
  }

  // Update message
  static async updateMessage(
    messageId: string,
    data: UpdateMessageData,
    userId: number
  ) {
    try {
      // Check if user is the author of the message
      const existingMessage = await prisma.forumMessage.findFirst({
        where: {
          id: messageId,
          authorId: userId,
          isDeleted: false,
        },
        include: {
          channel: true,
        },
      });

      if (!existingMessage) {
        throw new Error('Message not found or you are not the author');
      }

      const updatedMessage = await prisma.forumMessage.update({
        where: { id: messageId },
        data: {
          ...data,
          isEdited: true,
        },
        include: {
          author: {
            select: {
              id: true,
              fullName: true,
            },
          },
          attachments: true,
          reactions: {
            include: {
              staff: {
                select: {
                  id: true,
                  fullName: true,
                },
              },
            },
          },
        },
      });

      // Notify channel about message update
      sendToChannel(existingMessage.channelId!, 'message_updated', {
        messageId,
        updatedBy: userId,
        updatedAt: new Date(),
      });

      logger.info(`Message updated: ${messageId} by user ${userId}`);
      return updatedMessage;
    } catch (error) {
      logger.error('Error updating message:', error);
      throw error;
    }
  }

  // Delete message
  static async deleteMessage(messageId: string, userId: number) {
    try {
      // Check if user is the author or has admin/moderator permissions
      const message = await prisma.forumMessage.findFirst({
        where: {
          id: messageId,
          isDeleted: false,
        },
        include: {
          channel: {
            include: {
              group: {
                include: {
                  members: {
                    where: {
                      staffId: userId,
                      isActive: true,
                    },
                  },
                },
              },
            },
          },
        },
      });

      if (!message) {
        throw new Error('Message not found');
      }

      const isAuthor = message.authorId === userId;
      const isAdminOrModerator = message.channel?.group.members.some(
        (member) => member.role === 'ADMIN' || member.role === 'MODERATOR'
      );

      if (!isAuthor && !isAdminOrModerator) {
        throw new Error(
          'You can only delete your own messages or you must be an admin/moderator'
        );
      }

      await prisma.forumMessage.update({
        where: { id: messageId },
        data: { isDeleted: true },
      });

      // Notify channel about message deletion
      sendToChannel(message.channelId!, 'message_deleted', {
        messageId,
        deletedBy: userId,
        deletedAt: new Date(),
      });

      logger.info(`Message deleted: ${messageId} by user ${userId}`);
      return { success: true };
    } catch (error) {
      logger.error('Error deleting message:', error);
      throw error;
    }
  }

  // Add reaction to message
  static async addReaction(messageId: string, emoji: string, userId: number) {
    try {
      // Check if user has access to the message
      const message = await this.getMessageById(messageId, userId);

      // Check if user already reacted with this emoji
      const existingReaction = await prisma.forumMessageReaction.findUnique({
        where: {
          messageId_staffId_emoji: {
            messageId,
            staffId: userId,
            emoji,
          },
        },
      });

      if (existingReaction) {
        throw new Error('You have already reacted with this emoji');
      }

      const reaction = await prisma.forumMessageReaction.create({
        data: {
          messageId,
          staffId: userId,
          emoji,
        },
        include: {
          staff: {
            select: {
              id: true,
              fullName: true,
            },
          },
        },
      });

      // Notify channel about new reaction
      sendToChannel(message.channel!.id, 'reaction_added', {
        messageId,
        reaction,
        addedAt: new Date(),
      });

      logger.info(
        `Reaction added: ${emoji} to message ${messageId} by user ${userId}`
      );
      return reaction;
    } catch (error) {
      logger.error('Error adding reaction:', error);
      throw error;
    }
  }

  // Remove reaction from message
  static async removeReaction(
    messageId: string,
    emoji: string,
    userId: number
  ) {
    try {
      // Check if user has access to the message
      const message = await this.getMessageById(messageId, userId);

      await prisma.forumMessageReaction.delete({
        where: {
          messageId_staffId_emoji: {
            messageId,
            staffId: userId,
            emoji,
          },
        },
      });

      // Notify channel about reaction removal
      sendToChannel(message.channel!.id, 'reaction_removed', {
        messageId,
        emoji,
        userId,
        removedAt: new Date(),
      });

      logger.info(
        `Reaction removed: ${emoji} from message ${messageId} by user ${userId}`
      );
      return { success: true };
    } catch (error) {
      logger.error('Error removing reaction:', error);
      throw error;
    }
  }

  // Search messages in a channel
  static async searchMessages(
    channelId: number,
    query: string,
    userId: number,
    page = 1,
    limit = 20
  ) {
    try {
      // Check if user has access to the channel
      const channel = await prisma.forumChannel.findFirst({
        where: {
          id: channelId,
          isActive: true,
          group: {
            isActive: true,
            OR: [
              { isPrivate: false },
              {
                isPrivate: true,
                members: {
                  some: {
                    staffId: userId,
                    isActive: true,
                  },
                },
              },
            ],
          },
        },
      });

      if (!channel) {
        throw new Error('Channel not found or access denied');
      }

      const skip = (page - 1) * limit;

      const [messages, total] = await Promise.all([
        prisma.forumMessage.findMany({
          where: {
            channelId,
            isDeleted: false,
            content: {
              contains: query,
              mode: 'insensitive',
            },
          },
          include: {
            author: {
              select: {
                id: true,
                fullName: true,
              },
            },
            attachments: true,
            reactions: {
              include: {
                staff: {
                  select: {
                    id: true,
                    fullName: true,
                  },
                },
              },
            },
          },
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit,
        }),
        prisma.forumMessage.count({
          where: {
            channelId,
            isDeleted: false,
            content: {
              contains: query,
              mode: 'insensitive',
            },
          },
        }),
      ]);

      return {
        messages,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
        query,
      };
    } catch (error) {
      logger.error('Error searching messages:', error);
      throw error;
    }
  }
}
