import { db } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import { staffHasPermission, PERMISSIONS } from '../../utils/permission';
import {logger} from '../../utils/logger';

export interface CreateCommentData {
  content: string;
  ideaId: number;
}

export interface UpdateCommentData {
  content: string;
}

export const innovationIdeaCommentService = {
  createComment: async (staffId: number, reqBody: CreateCommentData) => {
    try {
      const { content, ideaId } = reqBody;

      if (!content || content.trim().length === 0) {
        throw new HttpError('Comment content is required', 400);
      }

      // Check if the innovation idea exists
      const idea = await db.innovationIdea.findUnique({
        where: { id: ideaId },
      });

      if (!idea) {
        throw new HttpError('Innovation idea not found', 404);
      }

      const comment = await db.innovationIdeaComment.create({
        data: {
          content,
          staffId,
          ideaId,
        },
      });

      return {
        message: 'Comment created successfully',
      };
    } catch (error) {
      logger.error('Error creating comment:', error);
      throw new HttpError('Failed to create comment', 400);
    }
  },

  deleteComment: async (staffId: number, commentId: number) => {
    try {
    await staffHasPermission(staffId, PERMISSIONS.HUB_EDIT);
      const comment = await db.innovationIdeaComment.findUnique({
        where: { id: commentId },
      });

      if (!comment) {
        throw new HttpError('Comment not found', 404);
      }

      await db.innovationIdeaComment.delete({
        where: { id: commentId },
      });

      return {
        message: 'Comment deleted successfully',
      };
    } catch (error) {
      logger.error('Error deleting comment:', error);
      throw new HttpError('Failed to delete comment', 400);
    }
  },
};
