import { Request, Response } from 'express';
import { GameParticipationService } from '../../services/game/participationService';
import { logger } from '../../utils/logger';

export class GameParticipationController {
  // Join a game
  static async joinGame(req: Request, res: Response) {
    try {
      const { gameId } = req.params;
      const staffId = req.Staff as unknown as number;

      if (!staffId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      const participant = await GameParticipationService.joinGame({
        gameId,
        staffId,
      });

      res.status(201).json({
        success: true,
        message: 'Successfully joined the game',
        data: participant,
      });
    } catch (error) {
      logger.error('Error joining game:', error);
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to join game',
      });
    }
  }

  // Leave a game
  static async leaveGame(req: Request, res: Response) {
    try {
      const { gameId } = req.params;
      const staffId = req.Staff as unknown as number;

      if (!staffId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      await GameParticipationService.leaveGame(gameId, staffId);

      res.status(200).json({
        success: true,
        message: 'Successfully left the game',
      });
    } catch (error) {
      logger.error('Error leaving game:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to leave game',
      });
    }
  }

  // Get participant's progress in a game
  static async getProgress(req: Request, res: Response) {
    try {
      const { gameId } = req.params;
      const staffId = req.Staff as unknown as number;

      if (!staffId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      const progress = await GameParticipationService.getParticipantProgress(
        gameId,
        staffId
      );

      res.status(200).json({
        success: true,
        data: progress,
      });
    } catch (error) {
      logger.error('Error getting participant progress:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to get progress',
      });
    }
  }

  // Submit answer to a question
  static async submitAnswer(req: Request, res: Response) {
    try {
      const { participantId, questionId, answer, timeSpent } = req.body;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      if (!participantId || !questionId || !answer) {
        res.status(400).json({
          success: false,
          message: 'Participant ID, question ID, and answer are required',
        });
      }

      const result = await GameParticipationService.submitAnswer({
        participantId: parseInt(participantId),
        questionId,
        answer,
        timeSpent,
      });

      res.status(201).json({
        success: true,
        message: 'Answer submitted successfully',
        data: result,
      });
    } catch (error) {
      logger.error('Error submitting answer:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to submit answer',
      });
    }
  }

  // Get participant's answers for a game
  static async getAnswers(req: Request, res: Response) {
    try {
      const { gameId } = req.params;
      const staffId = req.Staff as unknown as number;

      if (!staffId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      const answers = await GameParticipationService.getParticipantAnswers(
        gameId,
        staffId
      );

      res.status(200).json({
        success: true,
        data: answers,
      });
    } catch (error) {
      logger.error('Error getting participant answers:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to get answers',
      });
    }
  }

  // Get game leaderboard
  static async getLeaderboard(req: Request, res: Response) {
    try {
      const { gameId } = req.params;
      const { limit = 10 } = req.query;

      const leaderboard = await GameParticipationService.getGameLeaderboard(
        gameId,
        parseInt(limit as string)
      );

      res.status(200).json({
        success: true,
        data: leaderboard,
      });
    } catch (error) {
      logger.error('Error getting game leaderboard:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to get leaderboard',
      });
    }
  }

  // Get my participated games
  static async getMyParticipations(req: Request, res: Response) {
    try {
      const { status, page = 1, limit = 20 } = req.query;
      const staffId = req.Staff as unknown as number;

      if (!staffId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      // This would require a new service method to get participations by staff ID
      // For now, return a placeholder response
      res.status(200).json({
        success: true,
        message: 'My participations endpoint will be implemented',
        data: {
          participations: [],
          pagination: {
            page: parseInt(page as string),
            limit: parseInt(limit as string),
            total: 0,
            pages: 0,
          },
        },
      });
    } catch (error) {
      logger.error('Error getting my participations:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'Failed to get participations',
      });
    }
  }

  // Get participant statistics
  static async getParticipantStats(req: Request, res: Response) {
    try {
      const { gameId } = req.params;
      const staffId = req.Staff as unknown as number;

      if (!staffId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      const progress = await GameParticipationService.getParticipantProgress(
        gameId,
        staffId
      );

      // Calculate additional statistics
      const stats = {
        totalQuestions: progress.progress.totalQuestions,
        answeredQuestions: progress.progress.answeredQuestions,
        correctAnswers: progress.answers.filter((a) => a.isCorrect).length,
        incorrectAnswers: progress.answers.filter((a) => !a.isCorrect).length,
        totalScore: progress.participant.score,
        averageTimePerQuestion:
          progress.answers.length > 0
            ? progress.answers.reduce((sum, a) => sum + (a.timeSpent || 0), 0) /
              progress.answers.length
            : 0,
        accuracy:
          progress.answers.length > 0
            ? (progress.answers.filter((a) => a.isCorrect).length /
                progress.answers.length) *
              100
            : 0,
        completionPercentage: progress.progress.progressPercentage,
        status: progress.participant.status,
      };

      res.status(200).json({
        success: true,
        data: stats,
      });
    } catch (error) {
      logger.error('Error getting participant stats:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'Failed to get participant statistics',
      });
    }
  }

  // Get next question for participant
  static async getNextQuestion(req: Request, res: Response) {
    try {
      const { gameId } = req.params;
      const staffId = req.Staff as unknown as number;

      if (!staffId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      const progress = await GameParticipationService.getParticipantProgress(
        gameId,
        staffId
      );

      // Find the next unanswered question
      const answeredQuestionIds = progress.answers.map((a) => a.question.id);
      const nextQuestion = progress.game.questions.find(
        (q) => !answeredQuestionIds.includes(q.id)
      );

      if (!nextQuestion) {
        res.status(200).json({
          success: true,
          message: 'All questions have been answered',
          data: null,
        });
      }

      res.status(200).json({
        success: true,
        data: {
          question: nextQuestion,
          questionNumber: answeredQuestionIds.length + 1,
          totalQuestions: progress.game.questions.length,
          remainingQuestions: progress.progress.remainingQuestions,
        },
      });
    } catch (error) {
      logger.error('Error getting next question:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'Failed to get next question',
      });
    }
  }

  // Get participant ranking in game
  static async getParticipantRanking(req: Request, res: Response) {
    try {
      const { gameId } = req.params;
      const staffId = req.Staff as unknown as number;

      if (!staffId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      const leaderboard = await GameParticipationService.getGameLeaderboard(
        gameId,
        1000
      );
      const participantRank =
        leaderboard.findIndex((entry) => entry.staffId === staffId) + 1;

      if (participantRank === 0) {
        res.status(404).json({
          success: false,
          message: 'Participant not found in leaderboard',
        });
      }

      const participantEntry = leaderboard[participantRank - 1];

      res.status(200).json({
        success: true,
        data: {
          rank: participantRank,
          score: participantEntry.score,
          totalParticipants: leaderboard.length,
          percentile:
            ((leaderboard.length - participantRank + 1) / leaderboard.length) *
            100,
        },
      });
    } catch (error) {
      logger.error('Error getting participant ranking:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'Failed to get participant ranking',
      });
    }
  }
}
