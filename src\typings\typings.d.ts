import { Ad<PERSON>, Staff, User, ReferringEntity } from '@prisma/client';

export {};

declare global {
  namespace Express {
    interface Request {
      Admin?: Admin;
      Staff?: Staff;
      User?: User;
      Referrer?: ReferringEntity;
    }
    interface Response {
      success: (message: string, data?: any) => void;
      error: (message: string, data?: any, statusCode?: number) => void;
      global: (message: string) => void;
    }
  }
}

declare module 'socket.io' {
  interface Socket {
    userId?: number;
  }
}
