import { Request, Response } from 'express';
import { controllerOperations } from '../handlers/handleController';
import { priceModifierService } from '../../services/discount/priceModifier';

const VerifyPriceModifierHandler = (req: Request, res: Response) => {
  controllerOperations(
    priceModifierService.verifyPriceModifier,
    undefined,
    res,
    req.body
  );
};

const CreateDiscountHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    priceModifierService.createPriceModifier,
    req.body,
    res,
    staffId
  );
};

const UpdateDiscountHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    priceModifierService.updatePriceModifier,
    req.body,
    res,
    staffId
  );
};

const ListDiscountHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    priceModifierService.getAllDiscounts,
    req.query,
    res,
    staffId
  );
};

const ListDiscountRecordHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    priceModifierService.getAllDiscountRecords,
    req.query,
    res,
    staffId
  );
};

const DeactivateExpiredModifiersHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    priceModifierService.deactivateExpiredModifiers,
    undefined,
    res,
    staffId
  );
};

export const priceModifierControllers = {
  VerifyPriceModifierHandler,
  CreateDiscountHandler,
  ListDiscountRecordHandler,
  ListDiscountHandler,
  DeactivateExpiredModifiersHandler,
  UpdateDiscountHandler,
};
