# Staff Monthly Credit Reset Cron Job

## Overview

This document describes the automated cron job that resets all staff members' `monthlyCreditUsed` field to 0.0 at the beginning of each month.

## Implementation Details

### Files Created/Modified

1. **`src/v1/jobs/cronJobs/staffCreditReset.ts`** - Main cron job implementation
2. **`src/v1/services/staff/creditReset.ts`** - Service functions for credit reset operations
3. **`src/v1/jobs/cronJobs/index.ts`** - Updated to register the new cron job
4. **`src/v1/controllers/staff/staff.controller.ts`** - Added manual trigger endpoint
5. **`src/v1/routes/router/staff.route.ts`** - Added route for manual trigger

### Cron Job Schedule

- **Schedule**: `0 0 1 * *` (Runs at midnight on the 1st day of every month)
- **Timezone**: UTC (configurable)
- **Target**: All active staff members (`isActive: true`)

### Features

#### Automatic Monthly Reset

- Automatically resets `monthlyCreditUsed` to 0.0 for all active staff
- Runs on the first day of each month at midnight
- Comprehensive logging for monitoring and debugging
- Error handling with detailed error logging

#### Manual Trigger Endpoint

- **Endpoint**: `POST /api/v1/staff/reset-monthly-credit`
- **Authentication**: Required (secure middleware)
- **Permission**: Requires `STAFF_EDIT` permission
- **Response**: Returns count of updated staff records

### Usage

#### Automatic Operation

The cron job runs automatically when the application starts. No manual intervention is required.

#### Manual Trigger

To manually reset staff credit usage (for testing or administrative purposes):

```bash
curl -X POST \
  http://localhost:8080/api/v1/staff/reset-monthly-credit \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json'
```

**Response:**

```json
{
  "message": "Successfully reset monthly credit usage for 25 staff members",
  "count": 25
}
```

### Database Operation

The cron job performs the following database operation:

```sql
UPDATE "Staff"
SET "monthly_credit_used" = 0.0
WHERE "is_active" = true;
```

### Logging

The system logs the following events:

- Cron job start/completion
- Number of staff records updated
- Any errors that occur during the process

### Error Handling

- Database connection errors are logged and don't crash the application
- Permission errors for manual triggers return appropriate HTTP status codes
- Failed operations are retried on the next scheduled run

### Testing

Unit tests are provided in `src/v1/services/staff/__tests__/creditReset.test.ts` to verify:

- Successful credit reset operations
- Error handling scenarios
- Permission validation
- Database interaction mocking

### Monitoring

Monitor the cron job through:

- Application logs (search for "monthly staff credit reset")
- Database queries to verify `monthlyCreditUsed` values
- Manual endpoint testing

### Configuration

To modify the cron schedule, update the cron expression in `src/v1/jobs/cronJobs/staffCreditReset.ts`:

```typescript
// Current: Run at midnight on 1st of every month
'0 0 1 * *';

// Examples:
// '0 0 15 * *'  // Run at midnight on 15th of every month
// '0 2 1 * *'   // Run at 2 AM on 1st of every month
```

### Dependencies

- `node-cron`: For cron job scheduling
- `@prisma/client`: For database operations
- Existing permission system for manual triggers
- Existing logging infrastructure
