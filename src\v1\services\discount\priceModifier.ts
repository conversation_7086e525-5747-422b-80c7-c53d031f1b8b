import { db } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import { staffHasPermission, PERMISSIONS } from '../../utils/permission';
import { formatString } from '../../utils/stringFormatter';
import { createDateFilter } from '../../utils/util';
import { logger } from '../../utils/logger';

/**
 * Utility function to deactivate expired price modifiers
 * This can be called manually or by the cron job
 */
export const deactivateExpiredPriceModifiers = async (): Promise<number> => {
  try {
    // Get current date at the start of the day (midnight)
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Find all active price modifiers with an endDate that has passed
    const expiredModifiers = await db.packagePriceModifier.findMany({
      where: {
        isActive: true,
        endDate: {
          lt: today,
        },
      },
      select: {
        id: true,
        modifierCode: true,
        endDate: true,
      },
    });

    if (expiredModifiers.length === 0) {
      return 0;
    }

    const updateResult = await db.packagePriceModifier.updateMany({
      where: {
        id: {
          in: expiredModifiers.map((modifier) => modifier.id),
        },
      },
      data: {
        isActive: false,
      },
    });

    return updateResult.count;
  } catch (error) {
    logger.error('Error deactivating expired price modifiers:', error);
    throw error;
  }
};

export const priceModifierService = {
  verifyPriceModifier: async (reqBody: any) => {
    try {
    const { id, modifierCode } = reqBody;

    const pkg = await db.packageLocationPrice.findUnique({
      where: { id: Number(id) },
      include: {
        modifiers: true,
      },
    });

    if (!pkg) {
      throw new HttpError('Package price not found', 400);
    }

    // Find the matching modifier
    const modifier = pkg.modifiers.find(
      (mod) => mod.modifierCode === formatString.formatUpperCase(modifierCode)
    );

    if (!modifier) {
      throw new HttpError(
        'This discount code is not valid for the selected location.',
        400
      );
    }

    return {
      id: pkg.id,
      modifier,
    };
    } catch (error) {
    logger.error('Error verifying price modifier:', error);
    throw new HttpError('Failed to verify price modifier', 400);
  }
  },
  
  createPriceModifier: async (staffId: any, reqBody: any) => {
    try {
    await staffHasPermission(staffId, PERMISSIONS.REWARD_CREATE);
    const {
      code,
      startDate,
      endDate,
      amount,
      percentage,
      description,
      packages,
    } = reqBody;
    const formatCode = formatString.formatUpperCase(code);
    const codeExist = await db.packagePriceModifier.findUnique({
      where: {
        modifierCode: formatCode,
      },
    });

    if (codeExist) {
      throw new HttpError('Discount code already exist', 400);
    }

    await db.packagePriceModifier.create({
      data: {
        modifierCode: formatCode,
        amount,
        startDate,
        endDate,
        packageLocationPrice: {
          connect: packages.map((id: any) => ({ id: Number(id) })),
        },
        percentage,
        description,
      },
    });

    return { message: 'Discount created successfully' };
    } catch (error) {
    logger.error('Error creating price modifier:', error);
    throw new HttpError('Failed to create price modifier', 400);
  }
  },

  updatePriceModifier: async (staffId: any, reqBody: any) => {
    try {
    await staffHasPermission(staffId, PERMISSIONS.REWARD_EDIT);
    const { id, amount, endDate, isActive, packages } = reqBody;
    const codeExist = await db.packagePriceModifier.findUnique({
      where: {
        id: id,
      },
    });

    if (!codeExist) {
      throw new HttpError('Discount code does not exist', 400);
    }

    const updateData: any = {};

    if (amount !== undefined) updateData.amount = amount;
    if (endDate !== undefined) updateData.endDate = endDate;
    if (isActive !== undefined) updateData.isActive = isActive;
    if (packages !== undefined && Array.isArray(packages)) {
      updateData.packageLocationPrice = {
        set: [],
        connect: packages.map((pkgId: any) => ({ id: Number(pkgId) })),
      };
    }

    await db.packagePriceModifier.update({
      where: { id },
      data: updateData,
    });

    return { message: 'Discount updated successfully' };
    } catch (error) {
    logger.error('Error updating price modifier:', error);
    throw new HttpError('Failed to update price modifier', 400);
  }
  },

  getAllDiscounts: async (staffId: any, query: any) => {
    try {
    await staffHasPermission(staffId, PERMISSIONS.REWARD_VIEW);

    // Parse pagination parameters with defaults
    const page: number = parseInt(query.page as string);
    const limit: number = parseInt(query.limit as string);
    const search = query.search as string;
    const startDate = query.startDate as string;
    const endDate = query.endDate as string;

    const dateFilter = createDateFilter(startDate, endDate);

    const whereClause: any = {
      ...(search
        ? {
            OR: [{ modifierCode: { contains: search, mode: 'insensitive' } }],
          }
        : {}),
      ...dateFilter,
    };

    const [discounts, totalPages, totalCount] = await db.$transaction([
      db.packagePriceModifier.findMany({
        where: whereClause,
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
        include: {
          packageLocationPrice: {
            select: {
              id: true,
              amount: true,
              package: {
                select: {
                  id: true,
                  name: true,
                  totalSlot: true,
                },
              },
              location: {
                select: {
                  name: true,
                  region: true,
                },
              },
            },
          },
          _count: {
            select: {
              packageLocationPrice: true,
            },
          },
        },
      }),
      db.packagePriceModifier.count({
        where: whereClause,
      }),
      db.packagePriceModifier.count(),
    ]);

    const discountWithPriceCount = discounts.map((discount) => ({
      ...discount,
      packagePricesCount: discount._count.packageLocationPrice,
    }));

    return {
      discounts: discountWithPriceCount,
      totalPages: Math.ceil(totalPages / limit),
      totalCount: totalCount,
      currentPage: page,
      limit: limit,
    };
    } catch (error) {
      logger.error('Error getting all discounts:', error);
    throw new HttpError('Failed to fetch discounts', 400);
  }
  },

  getAllDiscountRecords: async (staffId: any, query: any) => {
    try {
    await staffHasPermission(staffId, PERMISSIONS.REWARD_VIEW);

    const page: number = parseInt(query.page as string) || 1;
    const limit: number = parseInt(query.limit as string) || 10;
    const startDate = query.startDate as string;
    const endDate = query.endDate as string;
    const search = query.search as string;

    const dateFilter = createDateFilter(startDate, endDate);
    const whereClause: any = {
      ...(search
        ? {
            OR: [
              { code: { contains: search, mode: 'insensitive' } },
              { package: { contains: search, mode: 'insensitive' } },
              {
                user: {
                  OR: [
                    { emailAddress: { contains: search, mode: 'insensitive' } },
                    { phoneNumber: { contains: search, mode: 'insensitive' } },
                  ],
                },
              },
            ],
          }
        : {}),
      ...dateFilter,
    };

    const [discounts, totalPages, totalCount] = await db.$transaction([
      db.discountRecord.findMany({
        where: whereClause,
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
        include: {
          user: {
            select: {
              emailAddress: true,
              phoneNumber: true,
            },
          },
        },
      }),
      db.discountRecord.count({
        where: whereClause,
      }),
      db.discountRecord.count(),
    ]);

    return {
      result: discounts,
      totalPages: Math.ceil(totalPages / limit),
      totalCount: totalCount,
      currentPage: page,
      limit: limit,
    };
    } catch (error) {
      logger.error('Error getting all discount records:', error);
    throw new HttpError('Failed to fetch discount records', 400);
  }
  },

  deactivateExpiredModifiers: async (staffId: any) => {
    try {
    await staffHasPermission(staffId, PERMISSIONS.REWARD_EDIT);

      const deactivatedCount = await deactivateExpiredPriceModifiers();

      return {
        message: `Successfully deactivated ${deactivatedCount} expired price modifiers`,
        count: deactivatedCount,
      };
    } catch (error) {
      logger.error('Error deactivating expired price modifiers:', error);
      throw new HttpError('Failed to deactivate expired price modifiers', 500);
    }
  },
};
