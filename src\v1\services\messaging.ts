import { sendToUser, broadcast } from './socket';
import { redis } from '../utils/cache';
import { logger } from '../utils/logger';

export const messagingService = {
  sendNotification: async (
    userId: number,
    message: string,
    type: 'info' | 'success' | 'warning' | 'error' = 'info'
  ) => {
    const notification = {
      id: Date.now(),
      message,
      type,
      timestamp: new Date().toISOString(),
    };

    sendToUser(userId, 'notification', notification);

    // Store in Redis for offline users
    await redis.lpush(`notifications:${userId}`, JSON.stringify(notification));
    await redis.expire(`notifications:${userId}`, 86400); // 24 hours

    logger.info(`Notification sent to user ${userId}: ${message}`);
  },

  broadcastAnnouncement: async (
    message: string,
    type: 'info' | 'success' | 'warning' | 'error' = 'info'
  ) => {
    const announcement = {
      id: Date.now(),
      message,
      type,
      timestamp: new Date().toISOString(),
    };

    broadcast('announcement', announcement);
    logger.info(`Announcement broadcasted: ${message}`);
  },

  getOfflineNotifications: async (userId: number) => {
    const notifications = await redis.lrange(`notifications:${userId}`, 0, -1);
    await redis.del(`notifications:${userId}`);
    return notifications.map((n) => JSON.parse(n));
  },
};
