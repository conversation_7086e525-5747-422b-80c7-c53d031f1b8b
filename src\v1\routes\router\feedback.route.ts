import { Router } from 'express';
import { secure } from '../../middleware/auth';
import { feedbackControllers } from '../../controllers/feedback/feedback.controller';

export const feedbackRoute = Router();

// Public route for creating feedback
feedbackRoute.post('/submit', feedbackControllers.CreateFeedbackHandler);

// Admin routes for managing feedback (require authentication)
feedbackRoute.get('/list', secure, feedbackControllers.ListFeedbackHandler);

feedbackRoute.patch(
  '/mark-as-read',
  secure,
  feedbackControllers.UpdateFeedbackHandler
);
