import { db } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import { createDateFilter } from '../../utils/util';
import { staffHasPermission, PERMISSIONS } from '../../utils/permission';
import { logger } from '../../utils/logger';

export const feedbackService = {
  createFeedback: async (reqBody: any) => {
    try {
      const { name, email, phone, type, rating, feedback } = reqBody;

      if (rating < 1 || rating > 5) {
        throw new HttpError('Rating must be between 1 and 5', 400);
      }

      await db.feedback.create({
        data: {
          name,
          email,
          phone,
          type,
          rating: Number(rating),
          feedback,
        },
      });

      return {
        message: 'Feedback submitted successfully',
      };
    } catch (error) {
      logger.error('Error submitting feedback:', error);
      throw new HttpError('Failed to submit feedback', 400);
    }
  },

  getAllFeedback: async (staffId: any, query: any = {}) => {
    try {
    await staffHasPermission(staffId, PERMISSIONS.FEEDBACK_VIEW);

      const page: number = parseInt(query.page as string) || 1;
      const limit: number = parseInt(query.limit as string) || 10;

      const type = query.type as string;
      const read =
        query.read === 'true'
          ? true
          : query.read === 'false'
            ? false
            : undefined;
      const search = query.search as string;
      const rating = query.rating
        ? parseInt(query.rating as string)
        : undefined;
      const startDate = query.startDate as string;
      const endDate = query.endDate as string;
          const dateFilter = createDateFilter(startDate, endDate);


      if (rating !== undefined && !isNaN(rating)) {
  if (rating < 1 || rating > 5) {
    throw new HttpError('Rating must be between 1 and 5', 400);
  }
}

const whereClause: any = {
  ...(type ? { type } : {}),
  ...(read !== undefined ? { read } : {}),
  ...(rating !== undefined && !isNaN(rating) ? { rating: Number(rating) } : {}),
  ...(search
    ? {
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } },
          { phone: { contains: search, mode: 'insensitive' } },
          { feedback: { contains: search, mode: 'insensitive' } },
        ],
      }
    : {}),
  ...dateFilter,
};

      const [feedbacks, totalCount] = await db.$transaction([
        db.feedback.findMany({
          where: whereClause,
          orderBy: { createdAt: 'desc' },
          skip: (page - 1) * limit,
          take: limit,
        }),
        db.feedback.count({
          where: whereClause,
        }),
      ]);

      return {
        feedbacks,
        totalPages: Math.ceil(totalCount / limit),
        totalCount,
        currentPage: page,
        limit,
      };
    } catch (error) {
      logger.error('Error getting all feedback:', error);
      throw new HttpError('Failed to fetch feedback entries', 400);
    }
  },

  // Mark feedback as read
  updateFeedback: async (staffId: any, reqBody: any) => {
    try {
    await staffHasPermission(staffId, PERMISSIONS.FEEDBACK_VIEW);

      const { feedbackId } = reqBody;

      const feedback = await db.feedback.findUnique({
        where: { id: feedbackId },
      });

      if (!feedback) {
        throw new HttpError('Feedback not found', 404);
      }

      // Only update if not already read
      if (feedback.read === false) {
        const updatedFeedback = await db.feedback.update({
          where: { id: feedbackId },
          data: { read: true },
        });

        return updatedFeedback;
      }

      return feedback;
    } catch (error) {
      logger.error('Error updating feedback:', error);
      throw new HttpError('Failed to update feedback', 400);
    }
  },
};
