import type { Request, Response, NextFunction } from 'express';
import { Prisma } from '@prisma/client';
import {
  PrismaClientKnownRequestError,
  PrismaClientUnknownRequestError,
  PrismaClientRustPanicError,
  PrismaClientInitializationError,
  PrismaClientValidationError,
} from '@prisma/client/runtime/library';
import { logger } from '../utils/logger';
import { AppError } from '../utils/error';

interface ErrorResponse {
  status: string;
  message: string;
  code?: string;
  data?: any;
  stack?: string;
}

export const errorHandler = (
  err: Error | AppError,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  let statusCode = 500;
  const response: ErrorResponse = {
    status: 'error',
    message: 'Something unexpected happened. Please try again later.',
  };

  // Handle custom AppError instances
  if (err instanceof AppError) {
    statusCode = err.statusCode;
    response.status = statusCode < 500 ? 'fail' : 'error';
    response.message = err.message;
    response.code = err.code;
    response.data = err.data;
  }

  // Handle all Prisma errors with generic messages
  else if (
    err instanceof PrismaClientKnownRequestError ||
    err instanceof PrismaClientUnknownRequestError ||
    err instanceof PrismaClientRustPanicError ||
    err instanceof PrismaClientInitializationError ||
    err instanceof PrismaClientValidationError ||
    err instanceof Prisma.PrismaClientKnownRequestError ||
    err instanceof Prisma.PrismaClientUnknownRequestError ||
    err instanceof Prisma.PrismaClientRustPanicError ||
    err instanceof Prisma.PrismaClientInitializationError ||
    err instanceof Prisma.PrismaClientValidationError ||
    err.name?.includes('Prisma')
  ) {
    statusCode = 500;
    response.status = 'error';
    response.message = 'Server error occurred. Please try again later.';
    response.code = 'SERVER_ERROR';
  }

  // Handle validation errors (e.g., from express-validator)
  else if (err.name === 'ValidationError') {
    statusCode = 400;
    response.status = 'fail';
    response.message = 'Invalid input provided';
    response.code = 'VALIDATION_ERROR';
  }

  // Handle JWT errors
  else if (err.name === 'JsonWebTokenError') {
    statusCode = 401;
    response.status = 'fail';
    response.message = 'Authentication failed';
    response.code = 'AUTHENTICATION_ERROR';
  } else if (err.name === 'TokenExpiredError') {
    statusCode = 401;
    response.status = 'fail';
    response.message = 'Session expired. Please login again.';
    response.code = 'SESSION_EXPIRED';
  }

  // Add stack trace in development environment
  // if (process.env.NODE_ENV === 'development') {
  //   response.stack = err.stack;
  // }

  // Log the error
  logger.error('Error occurred', {
    statusCode,
    ...response,
    path: req.path,
    method: req.method,
    ip: req.ip,
    userId: (req as any).user?.id,
    staffId: (req as any).staff?.id,
    requestId: (req as any).requestId,
    body: req.body,
    query: req.query,
    params: req.params,
    stack: err.stack,
  });

  res.status(statusCode).json(response);
};
