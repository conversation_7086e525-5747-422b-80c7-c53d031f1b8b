import { Request, Response } from 'express';
import { GameAnalyticsService } from '../../services/game/analyticsService';
import { logger } from '../../utils/logger';

export class GameAnalyticsController {
  // Get overall game statistics
  static async getGameStatistics(req: Request, res: Response) {
    try {
      const { startDate, endDate, locationId, gameType, createdById } =
        req.query;

      const filters: any = {};
      if (startDate) filters.startDate = new Date(startDate as string);
      if (endDate) filters.endDate = new Date(endDate as string);
      if (locationId) filters.locationId = parseInt(locationId as string);
      if (gameType) filters.gameType = gameType as string;
      if (createdById) filters.createdById = parseInt(createdById as string);

      const statistics = await GameAnalyticsService.getGameStatistics(filters);

      res.status(200).json({
        success: true,
        data: statistics,
      });
    } catch (error) {
      logger.error('Error getting game statistics:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'Failed to get game statistics',
      });
    }
  }

  // Get participation analytics
  static async getParticipationAnalytics(req: Request, res: Response) {
    try {
      const { startDate, endDate, locationId, gameType, createdById } =
        req.query;

      const filters: any = {};
      if (startDate) filters.startDate = new Date(startDate as string);
      if (endDate) filters.endDate = new Date(endDate as string);
      if (locationId) filters.locationId = parseInt(locationId as string);
      if (gameType) filters.gameType = gameType as string;
      if (createdById) filters.createdById = parseInt(createdById as string);

      const analytics =
        await GameAnalyticsService.getParticipationAnalytics(filters);

      res.status(200).json({
        success: true,
        data: analytics,
      });
    } catch (error) {
      logger.error('Error getting participation analytics:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'Failed to get participation analytics',
      });
    }
  }

  // Get reward analytics
  static async getRewardAnalytics(req: Request, res: Response) {
    try {
      const { startDate, endDate, locationId, gameType, createdById } =
        req.query;

      const filters: any = {};
      if (startDate) filters.startDate = new Date(startDate as string);
      if (endDate) filters.endDate = new Date(endDate as string);
      if (locationId) filters.locationId = parseInt(locationId as string);
      if (gameType) filters.gameType = gameType as string;
      if (createdById) filters.createdById = parseInt(createdById as string);

      const analytics = await GameAnalyticsService.getRewardAnalytics(filters);

      res.status(200).json({
        success: true,
        data: analytics,
      });
    } catch (error) {
      logger.error('Error getting reward analytics:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'Failed to get reward analytics',
      });
    }
  }

  // Get game performance analytics
  static async getGamePerformance(req: Request, res: Response) {
    try {
      const { gameId } = req.params;

      const analytics =
        await GameAnalyticsService.getGamePerformanceAnalytics(gameId);

      res.status(200).json({
        success: true,
        data: analytics,
      });
    } catch (error) {
      logger.error('Error getting game performance analytics:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'Failed to get game performance analytics',
      });
    }
  }

  // Get dashboard analytics (combined overview)
  static async getDashboardAnalytics(req: Request, res: Response) {
    try {
      const { startDate, endDate, locationId } = req.query;

      const filters: any = {};
      if (startDate) filters.startDate = new Date(startDate as string);
      if (endDate) filters.endDate = new Date(endDate as string);
      if (locationId) filters.locationId = parseInt(locationId as string);

      const [gameStats, participationStats, rewardStats] = await Promise.all([
        GameAnalyticsService.getGameStatistics(filters),
        GameAnalyticsService.getParticipationAnalytics(filters),
        GameAnalyticsService.getRewardAnalytics(filters),
      ]);

      const dashboardData = {
        overview: {
          totalGames: gameStats.totalGames,
          activeGames: gameStats.activeGames,
          totalParticipants: participationStats.totalParticipations,
          totalRewardsDistributed: rewardStats.totalRewardAmount,
        },
        games: {
          byType: gameStats.gamesByType,
          byStatus: gameStats.gamesByStatus,
          averageParticipants: gameStats.averageParticipantsPerGame,
        },
        participation: {
          completionRate: participationStats.completionRate,
          withdrawalRate: participationStats.withdrawalRate,
          topParticipants: participationStats.topParticipants,
          trends: participationStats.participationTrends,
        },
        rewards: {
          totalAmount: rewardStats.totalRewardAmount,
          averageAmount: rewardStats.averageRewardAmount,
          byType: rewardStats.rewardsByType,
          topWinners: rewardStats.topWinners,
          trends: rewardStats.rewardTrends,
        },
      };

      res.status(200).json({
        success: true,
        data: dashboardData,
      });
    } catch (error) {
      logger.error('Error getting dashboard analytics:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'Failed to get dashboard analytics',
      });
    }
  }

  // Get my game analytics (for game creators)
  static async getMyGameAnalytics(req: Request, res: Response) {
    try {
      const { startDate, endDate } = req.query;
      const createdById = req.Staff as unknown as number;

      if (!createdById) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      const filters: any = { createdById };
      if (startDate) filters.startDate = new Date(startDate as string);
      if (endDate) filters.endDate = new Date(endDate as string);

      const [gameStats, participationStats, rewardStats] = await Promise.all([
        GameAnalyticsService.getGameStatistics(filters),
        GameAnalyticsService.getParticipationAnalytics(filters),
        GameAnalyticsService.getRewardAnalytics(filters),
      ]);

      const myAnalytics = {
        gamesCreated: gameStats.totalGames,
        activeGames: gameStats.activeGames,
        completedGames: gameStats.completedGames,
        totalParticipants: participationStats.totalParticipations,
        averageParticipantsPerGame: gameStats.averageParticipantsPerGame,
        completionRate: participationStats.completionRate,
        totalRewardsDistributed: rewardStats.totalRewardAmount,
        gamesByType: gameStats.gamesByType,
        participationTrends: participationStats.participationTrends,
        rewardTrends: rewardStats.rewardTrends,
      };

      res.status(200).json({
        success: true,
        data: myAnalytics,
      });
    } catch (error) {
      logger.error('Error getting my game analytics:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'Failed to get your game analytics',
      });
    }
  }

  // Get location-specific analytics
  static async getLocationAnalytics(req: Request, res: Response) {
    try {
      const { locationId } = req.params;
      const { startDate, endDate } = req.query;

      const filters: any = { locationId: parseInt(locationId) };
      if (startDate) filters.startDate = new Date(startDate as string);
      if (endDate) filters.endDate = new Date(endDate as string);

      const [gameStats, participationStats, rewardStats] = await Promise.all([
        GameAnalyticsService.getGameStatistics(filters),
        GameAnalyticsService.getParticipationAnalytics(filters),
        GameAnalyticsService.getRewardAnalytics(filters),
      ]);

      const locationAnalytics = {
        location: {
          id: parseInt(locationId),
          totalGames: gameStats.totalGames,
          activeGames: gameStats.activeGames,
          totalParticipants: participationStats.totalParticipations,
          totalRewards: rewardStats.totalRewardAmount,
        },
        performance: {
          averageParticipantsPerGame: gameStats.averageParticipantsPerGame,
          completionRate: participationStats.completionRate,
          averageRewardPerGame:
            gameStats.totalGames > 0
              ? Number(rewardStats.totalRewardAmount) / gameStats.totalGames
              : 0,
        },
        breakdown: {
          gamesByType: gameStats.gamesByType,
          gamesByStatus: gameStats.gamesByStatus,
          rewardsByType: rewardStats.rewardsByType,
        },
        trends: {
          participation: participationStats.participationTrends,
          rewards: rewardStats.rewardTrends,
        },
      };

      res.status(200).json({
        success: true,
        data: locationAnalytics,
      });
    } catch (error) {
      logger.error('Error getting location analytics:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'Failed to get location analytics',
      });
    }
  }

  // Get game type comparison analytics
  static async getGameTypeComparison(req: Request, res: Response) {
    try {
      const { startDate, endDate, locationId } = req.query;

      const filters: any = {};
      if (startDate) filters.startDate = new Date(startDate as string);
      if (endDate) filters.endDate = new Date(endDate as string);
      if (locationId) filters.locationId = parseInt(locationId as string);

      const [gameStats, participationStats, rewardStats] = await Promise.all([
        GameAnalyticsService.getGameStatistics(filters),
        GameAnalyticsService.getParticipationAnalytics(filters),
        GameAnalyticsService.getRewardAnalytics(filters),
      ]);

      // Create comparison data by game type
      const gameTypes = gameStats.gamesByType.map((gt) => gt.type);
      const comparison = await Promise.all(
        gameTypes.map(async (gameType) => {
          const typeFilters = { ...filters, gameType };
          const [typeGameStats, typeParticipationStats, typeRewardStats] =
            await Promise.all([
              GameAnalyticsService.getGameStatistics(typeFilters),
              GameAnalyticsService.getParticipationAnalytics(typeFilters),
              GameAnalyticsService.getRewardAnalytics(typeFilters),
            ]);

          return {
            gameType,
            totalGames: typeGameStats.totalGames,
            totalParticipants: typeParticipationStats.totalParticipations,
            averageParticipants: typeGameStats.averageParticipantsPerGame,
            completionRate: typeParticipationStats.completionRate,
            totalRewards: typeRewardStats.totalRewardAmount,
            averageReward: typeRewardStats.averageRewardAmount,
          };
        })
      );

      res.status(200).json({
        success: true,
        data: {
          comparison,
          summary: {
            totalGameTypes: gameTypes.length,
            mostPopularType: gameStats.gamesByType.reduce((prev, current) =>
              prev.count > current.count ? prev : current
            ),
            highestRewardType: comparison.reduce((prev, current) =>
              prev.totalRewards > current.totalRewards ? prev : current
            ),
          },
        },
      });
    } catch (error) {
      logger.error('Error getting game type comparison:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'Failed to get game type comparison',
      });
    }
  }
}
