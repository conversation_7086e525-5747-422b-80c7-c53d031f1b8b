import { Request, Response } from 'express';
import { menuService } from '../../services/cafeteria/menu';
import { controllerOperations } from '../handlers/handleController';

const getAllMenuItems = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(menuService.getAllMenuItems, req.query, res, staffId);
};

const getAllMenuCategory = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(menuService.getMenuByCategory, undefined, res, staffId);
};

const createMenuItem = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(menuService.createMenuItem, req.body, res, staffId);
};

const updateMenuItem = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(menuService.updateMenuItem, req.body, res, staffId);
};

export const menuController = {
  getAllMenuItems,
  updateMenuItem,
  getAllMenuCategory,
  createMenuItem,
};
