import { db } from '../../utils/model';
import { staffHasPermission, PERMISSIONS } from '../../utils/permission';
import { HttpError } from '../../utils/httpError';
import { formatString } from '../../utils/stringFormatter';
import { logger } from '../../utils/logger';


export const PackageTestService = {
  getAllPackageTest: async () => {
    try {
    return await db.test.findMany({
      select: {
        id: true,
        name: true,
      },
    });
    } catch (error) {
    logger.error("Failed to get all package test",error);
    throw new HttpError('Failed to get all package test', 400);
  }
  },

  getAllInvestigationAdmin: async (staffId: any, query: any) => {
    try {
    await staffHasPermission(staffId, PERMISSIONS.PACKAGE_VIEW);
    const page: number = parseInt(query.page as string);
    const limit: number = parseInt(query.limit as string);
    const [investigation, totalPages] = await db.$transaction([
      db.test.findMany({
        orderBy: { name: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
      }),

      db.test.count(),
    ]);

    return {
      investigations: investigation,
      totalPages: Math.ceil(totalPages / limit),
      totalCount: totalPages,
    };
    } catch (error) {
    logger.error("Failed to get all investigation",error);
    throw new HttpError('Failed to get all investigation', 400);
  }
  },

  createInvestigation: async (staffId: any, reqBody: any) => {
    try {
    await staffHasPermission(staffId, PERMISSIONS.PACKAGE_CREATE);
    const formatName = formatString.trimString(reqBody.name);

    const testExist = await db.test.findUnique({
      where: { name: formatName },
    });
    if (testExist) {
      throw new HttpError('Investigation with the name already exists', 400);
    }
    await db.test.create({
      data: {
        name: formatName,
      },
    });
    return {
      message: `${formatName} - investigation created successfully`,
    };
    } catch (error) {
    logger.error("Failed to create investigation",error);
    throw new HttpError('Failed to create investigation', 400);
  }
  },

  deleteInvestigation: async (staffId: any, id: number) => {
    try {
    await staffHasPermission(staffId, PERMISSIONS.PACKAGE_DELETE);
    const testExist = await db.test.findUnique({
      where: { id: Number(id) },
    });
    if (!testExist) {
      throw new HttpError('Investigation does exists', 400);
    }
    await db.test.delete({
      where: { id: Number(id) },
    });
    return {
      message: `${testExist?.name} - investigation deleted successfully`,
    };
    } catch (error) {
    logger.error("Failed to delete investigation",error);
    throw new HttpError('Failed to delete investigation', 400);
  }
  },
};
