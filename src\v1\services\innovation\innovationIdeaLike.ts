import { db } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import { logger } from '../../utils/logger';

export const innovationIdeaLikeService = {
  toggleLike: async (staffId: number, ideaId: number) => {
    try {
      const idea = await db.innovationIdea.findUnique({
        where: { id: ideaId },
      });

      if (!idea) {
        throw new HttpError('Innovation idea not found', 404);
      }

      const existingLike = await db.innovationIdeaLike.findUnique({
        where: {
          staffId_ideaId: {
            staffId,
            ideaId,
          },
        },
      });

      if (existingLike) {
        await db.innovationIdeaLike.delete({
          where: {
            id: existingLike.id,
          },
        });

        const likeCount = await db.innovationIdeaLike.count({
          where: { ideaId },
        });

        return {
          message: 'Innovation idea unliked successfully',
        };
      } else {
        await db.innovationIdeaLike.create({
          data: {
            staffId,
            ideaId,
          },
        });

        // Get updated like count
        const likeCount = await db.innovationIdeaLike.count({
          where: { ideaId },
        });

        return {
          message: 'Innovation idea liked successfully',
        };
      }
    } catch (error) {
      logger.error('Error toggling like on innovation idea:', error);
      throw new HttpError('Failed to toggle like on innovation idea', 500);
    }
  },
};
