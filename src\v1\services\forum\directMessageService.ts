import { PrismaClient, MessageType } from '@prisma/client';
import { logger } from '../../utils/logger';
import { notifyDirectMessage } from '../socket';

const prisma = new PrismaClient();

export interface CreateDirectMessageData {
  content: string;
  messageType?: MessageType;
  senderId: number;
  receiverId: number;
  attachments?: {
    fileName: string;
    fileUrl: string;
    fileType: string;
    fileSize: number;
  }[];
}

export interface UpdateDirectMessageData {
  content?: string;
  isEdited?: boolean;
}

export class DirectMessageService {
  // Send a direct message
  static async sendDirectMessage(data: CreateDirectMessageData) {
    try {
      // Check if both users exist and are active
      const [sender, receiver] = await Promise.all([
        prisma.staff.findFirst({
          where: { id: data.senderId, isActive: true },
          select: { id: true, fullName: true, email: true },
        }),
        prisma.staff.findFirst({
          where: { id: data.receiverId, isActive: true },
          select: { id: true, fullName: true, email: true },
        }),
      ]);

      if (!sender || !receiver) {
        throw new Error('Sender or receiver not found');
      }

      if (data.senderId === data.receiverId) {
        throw new Error('Cannot send message to yourself');
      }

      const message = await prisma.directMessage.create({
        data: {
          content: data.content,
          messageType: data.messageType || 'TEXT',
          senderId: data.senderId,
          receiverId: data.receiverId,
          attachments: data.attachments
            ? {
                create: data.attachments,
              }
            : undefined,
        },
        include: {
          sender: {
            select: {
              id: true,
              fullName: true,
              email: true,
            },
          },
          receiver: {
            select: {
              id: true,
              fullName: true,
              email: true,
            },
          },
          attachments: true,
        },
      });

      // Send real-time notification to receiver
      notifyDirectMessage(data.receiverId, data.senderId, message);

      logger.info(
        `Direct message sent: ${message.id} from user ${data.senderId} to user ${data.receiverId}`
      );
      return message;
    } catch (error) {
      logger.error('Error sending direct message:', error);
      throw error;
    }
  }

  // Get conversation between two users
  static async getConversation(
    userId1: number,
    userId2: number,
    page = 1,
    limit = 50
  ) {
    try {
      if (userId1 === userId2) {
        throw new Error('Cannot get conversation with yourself');
      }

      const skip = (page - 1) * limit;

      const [messages, total] = await Promise.all([
        prisma.directMessage.findMany({
          where: {
            OR: [
              { senderId: userId1, receiverId: userId2 },
              { senderId: userId2, receiverId: userId1 },
            ],
            isDeleted: false,
          },
          include: {
            sender: {
              select: {
                id: true,
                fullName: true,
                email: true,
              },
            },
            receiver: {
              select: {
                id: true,
                fullName: true,
                email: true,
              },
            },
            attachments: true,
          },
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit,
        }),
        prisma.directMessage.count({
          where: {
            OR: [
              { senderId: userId1, receiverId: userId2 },
              { senderId: userId2, receiverId: userId1 },
            ],
            isDeleted: false,
          },
        }),
      ]);

      return {
        messages: messages.reverse(), // Reverse to show oldest first
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      logger.error('Error getting conversation:', error);
      throw error;
    }
  }

  // Get all conversations for a user
  static async getUserConversations(userId: number, page = 1, limit = 20) {
    try {
      const skip = (page - 1) * limit;

      // Get latest message for each conversation
      const conversations = await prisma.$queryRaw`
        WITH latest_messages AS (
          SELECT DISTINCT ON (
            CASE 
              WHEN sender_id = ${userId} THEN receiver_id 
              ELSE sender_id 
            END
          )
          id,
          content,
          message_type,
          is_read,
          created_at,
          sender_id,
          receiver_id,
          CASE 
            WHEN sender_id = ${userId} THEN receiver_id 
            ELSE sender_id 
          END as other_user_id
          FROM direct_messages 
          WHERE (sender_id = ${userId} OR receiver_id = ${userId}) 
            AND is_deleted = false
          ORDER BY 
            CASE 
              WHEN sender_id = ${userId} THEN receiver_id 
              ELSE sender_id 
            END,
            created_at DESC
        )
        SELECT 
          lm.*,
          s.first_name as sender_first_name,
          s.last_name as sender_last_name,
          s.email as sender_email,
          r.first_name as receiver_first_name,
          r.last_name as receiver_last_name,
          r.email as receiver_email,
          ou.first_name as other_user_first_name,
          ou.last_name as other_user_last_name,
          ou.email as other_user_email
        FROM latest_messages lm
        JOIN staff s ON s.id = lm.sender_id
        JOIN staff r ON r.id = lm.receiver_id
        JOIN staff ou ON ou.id = lm.other_user_id
        ORDER BY lm.created_at DESC
        LIMIT ${limit} OFFSET ${skip}
      `;

      const total = await prisma.directMessage.groupBy({
        by: ['senderId', 'receiverId'],
        where: {
          OR: [{ senderId: userId }, { receiverId: userId }],
          isDeleted: false,
        },
      });

      return {
        conversations,
        pagination: {
          page,
          limit,
          total: total.length,
          pages: Math.ceil(total.length / limit),
        },
      };
    } catch (error) {
      logger.error('Error getting user conversations:', error);
      throw error;
    }
  }

  // Mark messages as read
  static async markAsRead(senderId: number, receiverId: number) {
    try {
      const updatedMessages = await prisma.directMessage.updateMany({
        where: {
          senderId,
          receiverId,
          isRead: false,
        },
        data: {
          isRead: true,
          readAt: new Date(),
        },
      });

      logger.info(
        `Marked ${updatedMessages.count} messages as read from user ${senderId} to user ${receiverId}`
      );
      return { count: updatedMessages.count };
    } catch (error) {
      logger.error('Error marking messages as read:', error);
      throw error;
    }
  }

  // Get unread message count
  static async getUnreadCount(userId: number) {
    try {
      const count = await prisma.directMessage.count({
        where: {
          receiverId: userId,
          isRead: false,
          isDeleted: false,
        },
      });

      return { count };
    } catch (error) {
      logger.error('Error getting unread count:', error);
      throw error;
    }
  }

  // Update direct message
  static async updateMessage(
    messageId: string,
    data: UpdateDirectMessageData,
    userId: number
  ) {
    try {
      // Check if user is the sender of the message
      const existingMessage = await prisma.directMessage.findFirst({
        where: {
          id: messageId,
          senderId: userId,
          isDeleted: false,
        },
      });

      if (!existingMessage) {
        throw new Error('Message not found or you are not the sender');
      }

      const updatedMessage = await prisma.directMessage.update({
        where: { id: messageId },
        data: {
          ...data,
          isEdited: true,
        },
        include: {
          sender: {
            select: {
              id: true,
              fullName: true,
            },
          },
          receiver: {
            select: {
              id: true,
              fullName: true,
            },
          },
          attachments: true,
        },
      });

      logger.info(`Direct message updated: ${messageId} by user ${userId}`);
      return updatedMessage;
    } catch (error) {
      logger.error('Error updating direct message:', error);
      throw error;
    }
  }

  // Delete direct message
  static async deleteMessage(messageId: string, userId: number) {
    try {
      // Check if user is the sender of the message
      const existingMessage = await prisma.directMessage.findFirst({
        where: {
          id: messageId,
          senderId: userId,
          isDeleted: false,
        },
      });

      if (!existingMessage) {
        throw new Error('Message not found or you are not the sender');
      }

      await prisma.directMessage.update({
        where: { id: messageId },
        data: { isDeleted: true },
      });

      logger.info(`Direct message deleted: ${messageId} by user ${userId}`);
      return { success: true };
    } catch (error) {
      logger.error('Error deleting direct message:', error);
      throw error;
    }
  }

  // Search direct messages
  static async searchMessages(
    userId: number,
    query: string,
    page = 1,
    limit = 20
  ) {
    try {
      const skip = (page - 1) * limit;

      const [messages, total] = await Promise.all([
        prisma.directMessage.findMany({
          where: {
            OR: [{ senderId: userId }, { receiverId: userId }],
            isDeleted: false,
            content: {
              contains: query,
              mode: 'insensitive',
            },
          },
          include: {
            sender: {
              select: {
                id: true,
                fullName: true,
              },
            },
            receiver: {
              select: {
                id: true,
                fullName: true,
              },
            },
            attachments: true,
          },
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit,
        }),
        prisma.directMessage.count({
          where: {
            OR: [{ senderId: userId }, { receiverId: userId }],
            isDeleted: false,
            content: {
              contains: query,
              mode: 'insensitive',
            },
          },
        }),
      ]);

      return {
        messages,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
        query,
      };
    } catch (error) {
      logger.error('Error searching direct messages:', error);
      throw error;
    }
  }
}
