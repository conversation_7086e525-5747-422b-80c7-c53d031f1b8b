import { Router } from 'express';
import { forumGroupRoute } from './forum/group.route';
import { forumChannelRoute } from './forum/channel.route';
import { forumMessageRoute } from './forum/message.route';
import { directMessageRoute } from './forum/directMessage.route';

export const forumRoute = Router();

// Forum group routes
forumRoute.use('/groups', forumGroupRoute);

// Forum channel routes
forumRoute.use('/channels', forumChannelRoute);

// Forum message routes
forumRoute.use('/messages', forumMessageRoute);

// Direct message routes
forumRoute.use('/direct-messages', directMessageRoute);
