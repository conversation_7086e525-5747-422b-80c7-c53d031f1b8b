/**
 * Main application entry point
 * Initializes and configures the Express server with all middleware and routes
 */
import { logger } from './v1/utils/logger';
import { emailQueue } from './v1/utils/queue';
import { createApp, startServer } from './server/index';
import { setupMiddleware, setupErrorHandling } from './middleware';
import { setupBullBoard, loadQueueProcessors } from './queue';
import { setupRoutes } from './routes';
import { configureGracefulShutdown } from './utils/shutdown';
import { setupCronJobs } from './v1/jobs/cronJobs';
import { initializeSocket } from './v1/services/socket';

// Create Express application
const app = createApp();

// Set up middleware
setupMiddleware(app);

// Set up Bull Board for queue monitoring in both environments
try {
  setupBullBoard(app, [emailQueue]);
  logger.info('Bull Board UI set up for monitoring email queue');
} catch (error) {
  logger.error(
    'Failed to set up Bull Board UI, but continuing application startup:',
    error
  );
}

// Load queue processors with better error handling
try {
  loadQueueProcessors().catch((error) => {
    logger.error(
      'Failed to load queue processors, but continuing application startup:',
      error
    );
    // Don't let this error crash the application
  });
  logger.info('Queue processors loaded successfully');
} catch (error) {
  logger.error(
    'Error during queue processor setup, but continuing application startup:',
    error
  );
  // Don't let this error crash the application
}

// Set up cron jobs
try {
  setupCronJobs();
  logger.info('Cron jobs set up successfully');
} catch (error) {
  logger.error(
    'Failed to set up cron jobs, but continuing application startup:',
    error
  );
  // Don't let this error crash the application
}

// Set up API routes
setupRoutes(app);

// Set up error handling middleware
setupErrorHandling(app);

// Start the server
const server = startServer(app);

// Initialize Socket.IO
initializeSocket(server);

// Configure graceful shutdown
configureGracefulShutdown(server);

// Export server for testing purposes
export { app, server };
