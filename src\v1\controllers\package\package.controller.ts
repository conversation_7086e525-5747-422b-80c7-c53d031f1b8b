import { Request, Response } from 'express';
import { controllerOperations } from '../handlers/handleController';
import { withImageControllerOperations } from '../handlers/withImageController';
import { packageService } from '../../services/package/package';

const CreatePackageHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  withImageControllerOperations(
    packageService.createPackage,
    res,
    req.file?.path,
    staffId,
    req.body
  );
};

const ListPackageHandler = (req: Request, res: Response) => {
  controllerOperations(packageService.getAllPackages, req.query, res);
};

const SinglePackageHandler = (req: Request, res: Response) => {
  const { slug } = req.params;
  controllerOperations(packageService.getSinglePackage, req.query, res, slug);
};

const SinglePackageAdminHandler = (req: Request, res: Response) => {
  const { slug } = req.params;
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    packageService.getSinglePackageAdmin,
    slug,
    res,
    staffId
  );
};

const ListAllPackageForLinkHandler = (_req: Request, res: Response) => {
  controllerOperations(packageService.getAllPackageForLink, undefined, res);
};

const ListAllPackageForAdmin = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    packageService.getAllPackagesAdmin,
    req.query,
    res,
    staffId
  );
};

const UpdatePackageHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(packageService.updatePackage, req.body, res, staffId);
};

const UpdatePackageStatusHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    packageService.updatePackageStatus,
    req.body,
    res,
    staffId
  );
};

const UpdatePackageImageHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  withImageControllerOperations(
    packageService.updatePackageImage,
    res,
    req.file?.path,
    staffId,
    req.body
  );
};
export const packageControllers = {
  CreatePackageHandler,
  ListPackageHandler,
  SinglePackageHandler,
  ListAllPackageForLinkHandler,
  UpdatePackageHandler,
  UpdatePackageImageHandler,
  ListAllPackageForAdmin,
  SinglePackageAdminHandler,
  UpdatePackageStatusHandler,
};
