import { Request, Response } from 'express';
import { controllerOperations } from '../handlers/handleController';
import { staffService } from '../../services/staff';
import { roleService } from '../../services/staff/role';

const CreateRoleHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(roleService.createRole, req.body, res, staffId);
};

const CreatePermissionHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(roleService.createPermission, req.body, res, staffId);
};

const ListRoleHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(roleService.getAllRoles, undefined, res, staffId);
};

const ListPermissionHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(roleService.getAllPermission, undefined, res, staffId);
};

const UpdateRoleHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(roleService.updateRole, req.body, res, staffId);
};

// const UpdateStaffAdminHandler = (req: Request, res: Response) => {
//   const staffId = req.Staff as unknown as number;
//   controllerOperations(staffService.adminUpdateStaff, req.body, res, staffId);
// };

// const VerifyStaffCodeHandler = (req: Request, res: Response) => {
//   controllerOperations(staffService.verifyStaffCode, undefined, res, req.body);
// };

// const CheckStaffCodeHandler = (req: Request, res: Response) => {
//   controllerOperations(staffService.checkStaffCode, undefined, res, req.body);
// };

// const CreateDepartmentHandler = (req: Request, res: Response) => {
//   const staffId = req.Staff as unknown as number;
//   controllerOperations(
//     departmentService.createDepartment,
//     req.body,
//     res,
//     staffId
//   );
// };

// const CreateUnitHandler = (req: Request, res: Response) => {
//   const staffId = req.Staff as unknown as number;
//   controllerOperations(departmentService.createUnit, req.body, res, staffId);
// };

// const ListDepartmentHandler = (req: Request, res: Response) => {
//   const staffId = req.Staff as unknown as number;
//   controllerOperations(
//     departmentService.getAllDepartment,
//     req.query,
//     res,
//     staffId
//   );
// };

// const UpdateDepartmentHandler = (req: Request, res: Response) => {
//   const staffId = req.Staff as unknown as number;
//   controllerOperations(
//     departmentService.updateDepartment,
//     req.body,
//     res,
//     staffId
//   );
// };

// const ListSpecialtyHandler = (req: Request, res: Response) => {
//   controllerOperations(specialtyService.getAllSpecialty, undefined, res);
// };

// const ListConsultantsHandler = (req: Request, res: Response) => {
//   controllerOperations(staffService.getConsultants, undefined, res);
// };

// const CreateSpecialtyHandler = (req: Request, res: Response) => {
//   const staffId = req.Staff as unknown as number;
//   controllerOperations(
//     specialtyService.createSpecialty,
//     req.body,
//     res,
//     staffId
//   );
// };

export const roleControllers = {
  CreateRoleHandler,
  CreatePermissionHandler,
  ListRoleHandler,
  ListPermissionHandler,
  UpdateRoleHandler,
};
