import { Router } from 'express';
import { secure } from '../../../middleware/auth';
import { ForumChannelController } from '../../../controllers/forum/channelController';

export const forumChannelRoute = Router();

// Create a new channel
forumChannelRoute.post('/create', secure, ForumChannelController.createChannel);

// Get channel by ID
forumChannelRoute.get('/:channelId', secure, ForumChannelController.getChannel);

// Get channels in a group
forumChannelRoute.get(
  '/group/:groupId',
  secure,
  ForumChannelController.getGroupChannels
);

// Update channel
forumChannelRoute.patch(
  '/:channelId',
  secure,
  ForumChannelController.updateChannel
);

// Delete channel
forumChannelRoute.delete(
  '/:channelId',
  secure,
  ForumChannelController.deleteChannel
);

// Archive/unarchive channel
forumChannelRoute.patch(
  '/:channelId/archive',
  secure,
  ForumChannelController.archiveChannel
);

forumChannelRoute.patch(
  '/:channelId/unarchive',
  secure,
  ForumChannelController.unarchiveChannel
);

// Pin/unpin message in channel
forumChannelRoute.patch(
  '/:channelId/pin/:messageId',
  secure,
  ForumChannelController.pinMessage
);

forumChannelRoute.patch(
  '/:channelId/unpin/:messageId',
  secure,
  ForumChannelController.unpinMessage
);

// Get pinned messages
forumChannelRoute.get(
  '/:channelId/pinned',
  secure,
  ForumChannelController.getPinnedMessages
);
