import { Router } from 'express';
import { secure } from '../../../middleware/auth';
import { DirectMessageController } from '../../../controllers/forum/directMessageController';
import {
  forumUpload,
  handleMulterError,
} from '../../../utils/upload/forumUpload';

export const directMessageRoute = Router();

// Send a direct message
directMessageRoute.post('/send', secure, DirectMessageController.sendMessage);

// Send a direct message with file attachments
directMessageRoute.post(
  '/send-with-files',
  secure,
  forumUpload.array('files', 5),
  handleMulterError,
  DirectMessageController.sendMessageWithFiles
);

// Get conversation between two users
directMessageRoute.get(
  '/conversation/:userId',
  secure,
  DirectMessageController.getConversation
);

// Get all conversations for current user
directMessageRoute.get(
  '/conversations',
  secure,
  DirectMessageController.getUserConversations
);

// Mark messages as read
directMessageRoute.patch(
  '/conversation/:userId/read',
  secure,
  DirectMessageController.markAsRead
);

// Get unread message count
directMessageRoute.get(
  '/unread-count',
  secure,
  DirectMessageController.getUnreadCount
);

// Update direct message
directMessageRoute.patch(
  '/:messageId',
  secure,
  DirectMessageController.updateMessage
);

// Delete direct message
directMessageRoute.delete(
  '/:messageId',
  secure,
  DirectMessageController.deleteMessage
);

// Search direct messages
directMessageRoute.get(
  '/search',
  secure,
  DirectMessageController.searchMessages
);

// Get conversation summary
directMessageRoute.get(
  '/conversation/:userId/summary',
  secure,
  DirectMessageController.getConversationSummary
);

// Block user
directMessageRoute.post(
  '/block/:userId',
  secure,
  DirectMessageController.blockUser
);

// Unblock user
directMessageRoute.delete(
  '/block/:userId',
  secure,
  DirectMessageController.unblockUser
);
