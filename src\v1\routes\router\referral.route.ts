import { Router } from 'express';
import { secure, referrerAuth } from '../../middleware/auth';
import { referralControllers } from '../../controllers/referral/referral.controller';

export const referralRoute = Router();

referralRoute.post('/create-account', referralControllers.RegisterHandler);
referralRoute.post('/login', referralControllers.LoginHandler);
referralRoute.post('/set-password', referralControllers.SetPasswordHandler);
referralRoute.post(
  '/confirm-referrer',
  secure,
  referralControllers.ConfirmReferringEntityHandler
);

referralRoute.post(
  '/new-patient-referral',
  referrerAuth,
  referralControllers.ReferPatient
);

referralRoute.post(
  '/new-comment',
  referrerAuth,
  referralControllers.ReferralCommentHandler
);

referralRoute.get(
  '/list-all-referral',
  secure,
  referralControllers.ListAllReferralHandler
);

referralRoute.get(
  '/list-referral-reward',
  secure,
  referralControllers.ListReferralRewardsHandler
);

referralRoute.get(
  '/list-referring-entity',
  secure,
  referralControllers.ListReferringEntitiesHandler
);
referralRoute.get(
  '/list-public-entity',
  referrerAuth,
  referralControllers.ListPublicEntitiesHandler
);

referralRoute.get(
  '/validate-account/:validateAccount',
  referralControllers.validateAccountHandler
);

referralRoute.get('/profile', referrerAuth, referralControllers.AccountProfile);
referralRoute.get(
  '/list-referrals',
  referrerAuth,
  referralControllers.ListReferralHandler
);
referralRoute.get(
  '/statistics',
  referrerAuth,
  referralControllers.ReferralStats
);

referralRoute.get(
  '/check-email',
  referrerAuth,
  referralControllers.CheckExistitngPatient
);

referralRoute.get(
  '/details/:referralId',
  referrerAuth,
  referralControllers.SingleReferralhandler
);

referralRoute.patch(
  '/update',
  referrerAuth,
  referralControllers.UpdateReferralHandler
);
