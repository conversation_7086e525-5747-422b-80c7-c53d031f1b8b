import { Router } from 'express';
import { secure } from '../../middleware/auth';
import { transactionControllers } from '../../controllers/transaction/transaction.controller';

export const transactionRoute = Router();

transactionRoute.get(
  '/list-all-transaction',
  secure,
  transactionControllers.ListAllTransactions
);

transactionRoute.get(
  '/list-staff-transaction',
  secure,
  transactionControllers.ListAllStaffTransactions
);

transactionRoute.post(
  '/withdrawal-request',
  secure,
  transactionControllers.withdrawalRequest
);

transactionRoute.post(
  '/transfer-funds',
  secure,
  transactionControllers.transferFunds
);

transactionRoute.patch(
  '/withdrawal-request-update',
  secure,
  transactionControllers.withdrawalRequestUpdate
);

// Send meal voucher to specific staff
transactionRoute.post(
  '/send-meal-voucher/individual',
  secure,
  transactionControllers.SendVoucherHandler
);

// Send meal voucher to all staff in specific locations
transactionRoute.post(
  '/send-meal-voucher/location',
  secure,
  transactionControllers.SendLocationVoucherHandler
);
