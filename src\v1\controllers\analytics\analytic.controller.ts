import { Request, Response } from 'express';
import { controllerOperations } from '../handlers/handleController';
import { analyticsService } from '../../services/analytics';

const BookingGraphAnalytics = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    analyticsService.getBookingsGraph,
    req.query,
    res,
    staffId
  );
};

const BookingStatsAnalytics = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    analyticsService.getAllBookingsStatistics,
    null,
    res,
    staffId
  );
};

const DashboardAnalytics = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    analyticsService.getDashboardAnalytics,
    req.query,
    res,
    staffId
  );
};

const FeedbackAnalytics = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    analyticsService.getFeedbackAnalytics,
    req.query,
    res,
    staffId
  );
};

const StaffReferralAnalytics = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    analyticsService.getStaffReferralAnalytics,
    req.query,
    res,
    staffId
  );
};

const TransactionAnalytics = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    analyticsService.getTransactionAnalytics,
    req.query,
    res,
    staffId
  );
};

const DiscountAnalytics = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    analyticsService.getDiscountAnalytics,
    req.query,
    res,
    staffId
  );
};

export const analyticsControllers = {
  BookingGraphAnalytics,
  BookingStatsAnalytics,
  DashboardAnalytics,
  FeedbackAnalytics,
  StaffReferralAnalytics,
  TransactionAnalytics,
  DiscountAnalytics,
};
