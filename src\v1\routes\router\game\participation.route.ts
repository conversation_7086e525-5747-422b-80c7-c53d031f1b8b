import { Router } from 'express';
import { secure } from '../../../middleware/auth';
import { GameParticipationController } from '../../../controllers/game/participationController';

export const gameParticipationRoute = Router();

// Join a game
gameParticipationRoute.post(
  '/:gameId/join',
  secure,
  GameParticipationController.joinGame
);

// Leave a game
gameParticipationRoute.delete(
  '/:gameId/leave',
  secure,
  GameParticipationController.leaveGame
);

// Get participant's progress in a game
gameParticipationRoute.get(
  '/:gameId/progress',
  secure,
  GameParticipationController.getProgress
);

// Submit answer to a question
gameParticipationRoute.post(
  '/answer',
  secure,
  GameParticipationController.submitAnswer
);

// Get participant's answers for a game
gameParticipationRoute.get(
  '/:gameId/answers',
  secure,
  GameParticipationController.getAnswers
);

// Get game leaderboard
gameParticipationRoute.get(
  '/:gameId/leaderboard',
  secure,
  GameParticipationController.getLeaderboard
);

// Get my participated games
gameParticipationRoute.get(
  '/my/participations',
  secure,
  GameParticipationController.getMyParticipations
);

// Get participant statistics
gameParticipationRoute.get(
  '/:gameId/stats',
  secure,
  GameParticipationController.getParticipantStats
);

// Get next question for participant
gameParticipationRoute.get(
  '/:gameId/next-question',
  secure,
  GameParticipationController.getNextQuestion
);

// Get participant ranking in game
gameParticipationRoute.get(
  '/:gameId/ranking',
  secure,
  GameParticipationController.getParticipantRanking
);
