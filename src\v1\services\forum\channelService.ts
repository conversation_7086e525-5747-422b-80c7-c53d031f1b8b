import { PrismaClient } from '@prisma/client';
import { logger } from '../../utils/logger';
import { sendToChannel, sendToGroup } from '../socket';

const prisma = new PrismaClient();

export interface CreateChannelData {
  name: string;
  description?: string;
  isPrivate?: boolean;
  groupId: number;
  createdById: number;
}

export interface UpdateChannelData {
  name?: string;
  description?: string;
  isPrivate?: boolean;
  isActive?: boolean;
}

export class ForumChannelService {
  // Create a new channel
  static async createChannel(data: CreateChannelData) {
    try {
      // Check if user is member of the group
      const membership = await prisma.forumGroupMember.findFirst({
        where: {
          groupId: data.groupId,
          staffId: data.createdById,
          isActive: true,
          role: { in: ['ADMIN', 'MODERATOR'] },
        },
      });

      if (!membership) {
        throw new Error('Only group admins and moderators can create channels');
      }

      const channel = await prisma.forumChannel.create({
        data: {
          name: data.name,
          description: data.description,
          isPrivate: data.isPrivate || false,
          groupId: data.groupId,
          createdById: data.createdById,
        },
        include: {
          createdBy: {
            select: {
              id: true,
              fullName: true,
            },
          },
          group: {
            select: {
              id: true,
              name: true,
            },
          },
          _count: {
            select: {
              messages: true,
            },
          },
        },
      });

      // Notify group members about new channel
      sendToGroup(data.groupId, 'channel_created', {
        channel,
        createdAt: new Date(),
      });

      logger.info(
        `Channel created: ${channel.id} in group ${data.groupId} by user ${data.createdById}`
      );
      return channel;
    } catch (error) {
      logger.error('Error creating channel:', error);
      throw error;
    }
  }

  // Get channel by ID
  static async getChannelById(channelId: number, userId: number) {
    try {
      const channel = await prisma.forumChannel.findFirst({
        where: {
          id: channelId,
          isActive: true,
          group: {
            isActive: true,
            OR: [
              { isPrivate: false },
              {
                isPrivate: true,
                members: {
                  some: {
                    staffId: userId,
                    isActive: true,
                  },
                },
              },
            ],
          },
        },
        include: {
          createdBy: {
            select: {
              id: true,
              fullName: true,
            },
          },
          group: {
            select: {
              id: true,
              name: true,
              isPrivate: true,
            },
          },
          _count: {
            select: {
              messages: true,
              pinnedMessages: true,
            },
          },
        },
      });

      if (!channel) {
        throw new Error('Channel not found or access denied');
      }

      return channel;
    } catch (error) {
      logger.error('Error getting channel:', error);
      throw error;
    }
  }

  // Get channels in a group
  static async getGroupChannels(groupId: number, userId: number) {
    try {
      // Check if user has access to the group
      const group = await prisma.forumGroup.findFirst({
        where: {
          id: groupId,
          isActive: true,
          OR: [
            { isPrivate: false },
            {
              isPrivate: true,
              members: {
                some: {
                  staffId: userId,
                  isActive: true,
                },
              },
            },
          ],
        },
      });

      if (!group) {
        throw new Error('Group not found or access denied');
      }

      const channels = await prisma.forumChannel.findMany({
        where: {
          groupId,
          isActive: true,
        },
        include: {
          createdBy: {
            select: {
              id: true,
              fullName: true,
            },
          },
          _count: {
            select: {
              messages: true,
              pinnedMessages: true,
            },
          },
        },
        orderBy: { createdAt: 'asc' },
      });

      return channels;
    } catch (error) {
      logger.error('Error getting group channels:', error);
      throw error;
    }
  }

  // Update channel
  static async updateChannel(
    channelId: number,
    data: UpdateChannelData,
    userId: number
  ) {
    try {
      // Get channel with group info
      const channel = await prisma.forumChannel.findUnique({
        where: { id: channelId },
        include: { group: true },
      });

      if (!channel) {
        throw new Error('Channel not found');
      }

      // Check if user is admin or moderator of the group
      const membership = await prisma.forumGroupMember.findFirst({
        where: {
          groupId: channel.groupId,
          staffId: userId,
          role: { in: ['ADMIN', 'MODERATOR'] },
          isActive: true,
        },
      });

      if (!membership) {
        throw new Error('Only group admins and moderators can update channels');
      }

      const updatedChannel = await prisma.forumChannel.update({
        where: { id: channelId },
        data,
        include: {
          createdBy: {
            select: {
              id: true,
              fullName: true,
            },
          },
          group: {
            select: {
              id: true,
              name: true,
            },
          },
          _count: {
            select: {
              messages: true,
            },
          },
        },
      });

      // Notify channel members about the update
      sendToChannel(channelId, 'channel_updated', {
        channelId,
        updatedBy: userId,
        changes: data,
        updatedAt: new Date(),
      });

      logger.info(`Channel updated: ${channelId} by user ${userId}`);
      return updatedChannel;
    } catch (error) {
      logger.error('Error updating channel:', error);
      throw error;
    }
  }

  // Delete channel
  static async deleteChannel(channelId: number, userId: number) {
    try {
      // Get channel with group info
      const channel = await prisma.forumChannel.findUnique({
        where: { id: channelId },
        include: { group: true },
      });

      if (!channel) {
        throw new Error('Channel not found');
      }

      // Check if user is admin of the group
      const membership = await prisma.forumGroupMember.findFirst({
        where: {
          groupId: channel.groupId,
          staffId: userId,
          role: 'ADMIN',
          isActive: true,
        },
      });

      if (!membership) {
        throw new Error('Only group admins can delete channels');
      }

      // Soft delete the channel
      await prisma.forumChannel.update({
        where: { id: channelId },
        data: { isActive: false },
      });

      // Notify group members about channel deletion
      sendToGroup(channel.groupId, 'channel_deleted', {
        channelId,
        deletedBy: userId,
        deletedAt: new Date(),
      });

      logger.info(`Channel deleted: ${channelId} by user ${userId}`);
      return { success: true };
    } catch (error) {
      logger.error('Error deleting channel:', error);
      throw error;
    }
  }

  // Pin message in channel
  static async pinMessage(
    channelId: number,
    messageId: string,
    userId: number
  ) {
    try {
      // Get channel with group info
      const channel = await prisma.forumChannel.findUnique({
        where: { id: channelId },
        include: { group: true },
      });

      if (!channel) {
        throw new Error('Channel not found');
      }

      // Check if user is admin or moderator of the group
      const membership = await prisma.forumGroupMember.findFirst({
        where: {
          groupId: channel.groupId,
          staffId: userId,
          role: { in: ['ADMIN', 'MODERATOR'] },
          isActive: true,
        },
      });

      if (!membership) {
        throw new Error('Only group admins and moderators can pin messages');
      }

      // Check if message exists and belongs to this channel
      const message = await prisma.forumMessage.findFirst({
        where: {
          id: messageId,
          channelId,
          isDeleted: false,
        },
      });

      if (!message) {
        throw new Error('Message not found in this channel');
      }

      const pinnedMessage = await prisma.forumPinnedMessage.create({
        data: {
          channelId,
          messageId,
          pinnedById: userId,
        },
        include: {
          message: {
            include: {
              author: {
                select: {
                  id: true,
                  fullName: true,
                },
              },
            },
          },
        },
      });

      // Notify channel members about pinned message
      sendToChannel(channelId, 'message_pinned', {
        channelId,
        messageId,
        pinnedBy: userId,
        pinnedAt: new Date(),
      });

      logger.info(
        `Message pinned: ${messageId} in channel ${channelId} by user ${userId}`
      );
      return pinnedMessage;
    } catch (error) {
      logger.error('Error pinning message:', error);
      throw error;
    }
  }

  // Unpin message in channel
  static async unpinMessage(
    channelId: number,
    messageId: string,
    userId: number
  ) {
    try {
      // Get channel with group info
      const channel = await prisma.forumChannel.findUnique({
        where: { id: channelId },
        include: { group: true },
      });

      if (!channel) {
        throw new Error('Channel not found');
      }

      // Check if user is admin or moderator of the group
      const membership = await prisma.forumGroupMember.findFirst({
        where: {
          groupId: channel.groupId,
          staffId: userId,
          role: { in: ['ADMIN', 'MODERATOR'] },
          isActive: true,
        },
      });

      if (!membership) {
        throw new Error('Only group admins and moderators can unpin messages');
      }

      await prisma.forumPinnedMessage.delete({
        where: {
          messageId,
        },
      });

      // Notify channel members about unpinned message
      sendToChannel(channelId, 'message_unpinned', {
        channelId,
        messageId,
        unpinnedBy: userId,
        unpinnedAt: new Date(),
      });

      logger.info(
        `Message unpinned: ${messageId} in channel ${channelId} by user ${userId}`
      );
      return { success: true };
    } catch (error) {
      logger.error('Error unpinning message:', error);
      throw error;
    }
  }

  // Get pinned messages in channel
  static async getPinnedMessages(channelId: number, userId: number) {
    try {
      // Check if user has access to the channel
      await this.getChannelById(channelId, userId);

      const pinnedMessages = await prisma.forumPinnedMessage.findMany({
        where: { channelId },
        include: {
          message: {
            include: {
              author: {
                select: {
                  id: true,
                  fullName: true,
                },
              },
              attachments: true,
              reactions: {
                include: {
                  staff: {
                    select: {
                      id: true,
                      fullName: true,
                    },
                  },
                },
              },
            },
          },
          pinnedBy: {
            select: {
              id: true,
              fullName: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      });

      return pinnedMessages;
    } catch (error) {
      logger.error('Error getting pinned messages:', error);
      throw error;
    }
  }

  // Get channel statistics
  static async getChannelStats(channelId: number) {
    try {
      const [messageCount, memberCount, recentActivity] = await Promise.all([
        // Total messages in channel
        prisma.forumMessage.count({
          where: {
            channelId,
            isDeleted: false,
          },
        }),
        // Member count (from group)
        prisma.forumChannel.findUnique({
          where: { id: channelId },
          include: {
            group: {
              include: {
                _count: {
                  select: {
                    members: {
                      where: { isActive: true },
                    },
                  },
                },
              },
            },
          },
        }),
        // Recent activity (messages in last 7 days)
        prisma.forumMessage.count({
          where: {
            channelId,
            isDeleted: false,
            createdAt: {
              gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
            },
          },
        }),
      ]);

      return {
        messageCount,
        memberCount: memberCount?.group._count.members || 0,
        recentActivity,
        channelInfo: {
          id: memberCount?.id,
          name: memberCount?.name,
          description: memberCount?.description,
          isPrivate: memberCount?.isPrivate,
          createdAt: memberCount?.createdAt,
        },
      };
    } catch (error) {
      logger.error('Error getting channel stats:', error);
      throw error;
    }
  }
}
