import { Request, Response } from 'express';
import { inventoryService } from '../../services/cafeteria/inventory';
import { controllerOperations } from '../handlers/handleController';
import { withImageControllerOperations } from '../handlers/withImageController';

const getAllInventoryItems = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    inventoryService.getAllInventoryItems,
    req.query,
    res,
    staffId
  );
};

const getAllInventoryCat = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    inventoryService.getInventoryCategory,
    undefined,
    res,
    staffId
  );
};

const createInventorycat = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    inventoryService.createInventoryCategory,
    req.body,
    res,
    staffId
  );
};

const createInventoryItem = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    inventoryService.createInventoryItem,
    req.body,
    res,
    staffId
  );
};

const issueStockItem = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(inventoryService.issueInventory, req.body, res, staffId);
};

const addInventorySupply = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  withImageControllerOperations(
    inventoryService.addSupply,
    res,
    req.file?.path,
    staffId,
    req.body
  );
};

export const getSupplyHistory = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  const { inventoryId } = req.params;
  controllerOperations(
    inventoryService.getSupplyHistory,
    inventoryId,
    res,
    staffId
  );
};

const getIssuedStock = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    inventoryService.getStockIssued,
    req.query,
    res,
    staffId
  );
};

export const inventoryController = {
  getAllInventoryItems,
  createInventoryItem,
  addInventorySupply,
  getSupplyHistory,
  createInventorycat,
  getAllInventoryCat,
  issueStockItem,
  getIssuedStock,
};
