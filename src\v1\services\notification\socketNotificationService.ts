// socketNotificationService.ts
import { sendToUser, broadcast, sendToChannel, sendToGroup } from '../socket';
import { logger } from '../../utils/logger';

export interface NotificationData {
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  data?: any;
  timestamp?: Date;
}

export interface SystemNotificationData extends NotificationData {
  priority: 'low' | 'medium' | 'high' | 'critical';
  category: string;
}

const withTimestamp = (data: NotificationData): NotificationData => ({
  ...data,
  timestamp: data.timestamp || new Date(),
});

// --- Core notification functions ---
const notifyUser = (userId: number, event: string, data: NotificationData) => {
  try {
    sendToUser(userId, event, withTimestamp(data));
    logger.info(`Socket notification sent to user ${userId}: ${event}`);
  } catch (error) {
    logger.error(`Failed to send socket notification to user ${userId}:`, error);
  }
};

const broadcastNotification = (event: string, data: NotificationData) => {
  try {
    broadcast(event, withTimestamp(data));
    logger.info(`Socket notification broadcasted: ${event}`);
  } catch (error) {
    logger.error(`Failed to broadcast socket notification:`, error);
  }
};

const notifyChannel = (channelId: number, event: string, data: NotificationData) => {
  try {
    sendToChannel(channelId, event, withTimestamp(data));
    logger.info(`Socket notification sent to channel ${channelId}: ${event}`);
  } catch (error) {
    logger.error(`Failed to send socket notification to channel ${channelId}:`, error);
  }
};

const notifyGroup = (groupId: number, event: string, data: NotificationData) => {
  try {
    sendToGroup(groupId, event, withTimestamp(data));
    logger.info(`Socket notification sent to group ${groupId}: ${event}`);
  } catch (error) {
    logger.error(`Failed to send socket notification to group ${groupId}:`, error);
  }
};

// --- Specific Notification Types ---
const notifyTransactionUpdate = (userId: number, transactionData: any) =>
  notifyUser(userId, 'transaction_update', {
    title: 'Transaction Update',
    message: `Your ${transactionData.type.toLowerCase()} has been ${transactionData.status.toLowerCase()}`,
    type:
      transactionData.status === 'SUCCESS'
        ? 'success'
        : transactionData.status === 'FAILED'
        ? 'error'
        : 'info',
    data: transactionData,
  });

const notifyWalletUpdate = (userId: number, walletData: any) =>
  notifyUser(userId, 'wallet_update', {
    title: 'Wallet Update',
    message: `Your wallet balance has been updated`,
    type: 'info',
    data: walletData,
  });

const notifyOrderUpdate = (userId: number, orderData: any) =>
  notifyUser(userId, 'order_update', {
    title: 'Order Update',
    message: `Your order ${orderData.orderNumber} has been ${orderData.status.toLowerCase()}`,
    type: orderData.status === 'COMPLETED' ? 'success' : 'info',
    data: orderData,
  });

const notifyBookingUpdate = (bookingData: any) =>
  broadcastNotification('booking_update', {
    title: 'New Booking',
    message: `New package booking for ${bookingData.packageName}`,
    type: 'info',
    data: bookingData,
  });

const notifyStaffUpdate = (staffData: any) =>
  broadcastNotification('staff_update', {
    title: 'Staff Update',
    message: `Staff member ${staffData.fullName} has been updated`,
    type: 'info',
    data: staffData,
  });

const notifySystemUpdate = (data: SystemNotificationData) =>
  broadcastNotification('system_notification', {
    title: data.title,
    message: data.message,
    type: data.type,
    data: {
      ...data.data,
      priority: data.priority,
      category: data.category,
    },
  });

const notifyGameUpdate = (gameData: any) =>
  broadcastNotification('game_update', {
    title: 'Game Update',
    message: `Game "${gameData.title}" has been ${gameData.status.toLowerCase()}`,
    type: 'info',
    data: gameData,
  });

const notifyReferralUpdate = (referralData: any) =>
  broadcastNotification('referral_update', {
    title: 'Referral Update',
    message: `Referral ${referralData.referralID} has been ${referralData.status.toLowerCase()}`,
    type: 'info',
    data: referralData,
  });

const notifyRewardReceived = (userId: number, rewardData: any) =>
  notifyUser(userId, 'reward_received', {
    title: 'Reward Received!',
    message: `Congratulations! You've received a reward of ${rewardData.amount}`,
    type: 'success',
    data: rewardData,
  });

const notifyEmergency = (data: NotificationData) =>
  broadcastNotification('emergency_notification', {
    ...data,
    type: 'error',
  });

const notifyMaintenance = (data: NotificationData) =>
  broadcastNotification('maintenance_notification', {
    ...data,
    type: 'warning',
  });

const notifyMultipleUsers = (userIds: number[], event: string, data: NotificationData) =>
  userIds.forEach((userId) => notifyUser(userId, event, data));

const notifyLocationStaff = (locationId: number, event: string, data: NotificationData) =>
  broadcastNotification(event, {
    ...data,
    data: {
      ...data.data,
      locationId,
      locationSpecific: true,
    },
  });

const notifyDepartmentStaff = (departmentId: number, event: string, data: NotificationData) =>
  broadcastNotification(event, {
    ...data,
    data: {
      ...data.data,
      departmentId,
      departmentSpecific: true,
    },
  });

const notifyByRole = (role: string, event: string, data: NotificationData) =>
  broadcastNotification(event, {
    ...data,
    data: {
      ...data.data,
      targetRole: role,
      roleSpecific: true,
    },
  });


export const SocketNotificationService = {
  notifyUser,
  broadcastNotification,
  notifyChannel,
  notifyGroup,
  notifyTransactionUpdate,
  notifyWalletUpdate,
  notifyOrderUpdate,
  notifyBookingUpdate,
  notifyStaffUpdate,
  notifySystemUpdate,
  notifyGameUpdate,
  notifyReferralUpdate,
  notifyRewardReceived,
  notifyEmergency,
  notifyMaintenance,
  notifyMultipleUsers,
  notifyLocationStaff,
  notifyDepartmentStaff,
  notifyByRole,
};
