import { PrismaClient } from '@prisma/client';
import { logger } from '../../utils/logger';
import path from 'path';
import fs from 'fs';
import { v4 as uuidv4 } from 'uuid';

const cloudinary = require('../../utils/image/imageStorage');
const prisma = new PrismaClient();

export interface FileUploadData {
  originalName: string;
  mimetype: string;
  size: number;
  buffer?: Buffer;
  path?: string;
}

export interface UploadedFile {
  id: string;
  fileName: string;
  fileUrl: string;
  fileType: string;
  fileSize: number;
  cloudinaryPublicId?: string;
}

export interface FileValidationOptions {
  maxSize?: number; // in bytes
  allowedTypes?: string[];
  allowedExtensions?: string[];
}

export class FileUploadService {
  // Default validation options
  private static readonly DEFAULT_OPTIONS: FileValidationOptions = {
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain',
      'audio/mpeg',
      'audio/wav',
      'audio/ogg',
      'video/mp4',
      'video/webm',
      'video/ogg',
    ],
    allowedExtensions: [
      '.jpg',
      '.jpeg',
      '.png',
      '.gif',
      '.webp',
      '.pdf',
      '.doc',
      '.docx',
      '.xls',
      '.xlsx',
      '.txt',
      '.mp3',
      '.wav',
      '.ogg',
      '.mp4',
      '.webm',
    ],
  };

  // Validate file before upload
  static validateFile(
    file: FileUploadData,
    options?: FileValidationOptions
  ): void {
    const opts = { ...this.DEFAULT_OPTIONS, ...options };

    // Check file size
    if (opts.maxSize && file.size > opts.maxSize) {
      throw new Error(
        `File size exceeds maximum allowed size of ${opts.maxSize / (1024 * 1024)}MB`
      );
    }

    // Check MIME type
    if (opts.allowedTypes && !opts.allowedTypes.includes(file.mimetype)) {
      throw new Error(`File type ${file.mimetype} is not allowed`);
    }

    // Check file extension
    if (opts.allowedExtensions) {
      const ext = path.extname(file.originalName).toLowerCase();
      if (!opts.allowedExtensions.includes(ext)) {
        throw new Error(`File extension ${ext} is not allowed`);
      }
    }
  }

  // Get file category based on MIME type
  static getFileCategory(mimetype: string): string {
    if (mimetype.startsWith('image/')) return 'image';
    if (mimetype.startsWith('video/')) return 'video';
    if (mimetype.startsWith('audio/')) return 'audio';
    if (mimetype.includes('pdf')) return 'document';
    if (
      mimetype.includes('word') ||
      mimetype.includes('excel') ||
      mimetype.includes('text')
    )
      return 'document';
    return 'file';
  }

  // Upload file to Cloudinary
  static async uploadToCloudinary(
    file: FileUploadData,
    folder: string,
    publicId?: string
  ): Promise<{ url: string; publicId: string }> {
    try {
      const category = this.getFileCategory(file.mimetype);
      const resourceType =
        category === 'video'
          ? 'video'
          : category === 'audio'
            ? 'video'
            : 'auto';

      const uploadOptions: any = {
        folder: `forum/${folder}`,
        resource_type: resourceType,
        public_id: publicId || `${uuidv4()}_${Date.now()}`,
      };

      // Add transformations for images
      if (category === 'image') {
        uploadOptions.transformation = [
          { quality: 'auto', format: 'auto' },
          { width: 1920, height: 1080, crop: 'limit' },
        ];
      }

      let result;
      if (file.path) {
        // Upload from file path
        result = await cloudinary.uploader.upload(file.path, uploadOptions);
      } else if (file.buffer) {
        // Upload from buffer
        result = await new Promise((resolve, reject) => {
          cloudinary.uploader
            .upload_stream(uploadOptions, (error: any, result: any) => {
              if (error) reject(error);
              else resolve(result);
            })
            .end(file.buffer);
        });
      } else {
        throw new Error('No file data provided');
      }

      return {
        url: result.secure_url,
        publicId: result.public_id,
      };
    } catch (error) {
      logger.error('Error uploading to Cloudinary:', error);
      throw new Error('Failed to upload file to cloud storage');
    }
  }

  // Upload file locally as fallback
  static async uploadLocally(
    file: FileUploadData,
    folder: string
  ): Promise<{ url: string; filePath: string }> {
    try {
      const uploadsDir = path.join(process.cwd(), 'uploads', folder);

      // Ensure directory exists
      if (!fs.existsSync(uploadsDir)) {
        fs.mkdirSync(uploadsDir, { recursive: true });
      }

      const ext = path.extname(file.originalName);
      const fileName = `${uuidv4()}_${Date.now()}${ext}`;
      const filePath = path.join(uploadsDir, fileName);

      if (file.path) {
        // Copy from temporary path
        fs.copyFileSync(file.path, filePath);
      } else if (file.buffer) {
        // Write buffer to file
        fs.writeFileSync(filePath, file.buffer);
      } else {
        throw new Error('No file data provided');
      }

      return {
        url: `/uploads/${folder}/${fileName}`,
        filePath: `uploads/${folder}/${fileName}`,
      };
    } catch (error) {
      logger.error('Error uploading locally:', error);
      throw new Error('Failed to upload file locally');
    }
  }

  // Main upload method
  static async uploadFile(
    file: FileUploadData,
    folder: string,
    options?: FileValidationOptions & {
      useCloudinary?: boolean;
      publicId?: string;
    }
  ): Promise<UploadedFile> {
    try {
      // Validate file
      this.validateFile(file, options);

      const useCloudinary = options?.useCloudinary !== false; // Default to true
      let fileUrl: string;
      let cloudinaryPublicId: string | undefined;

      if (useCloudinary) {
        try {
          const cloudinaryResult = await this.uploadToCloudinary(
            file,
            folder,
            options?.publicId
          );
          fileUrl = cloudinaryResult.url;
          cloudinaryPublicId = cloudinaryResult.publicId;
        } catch (error) {
          logger.warn(
            'Cloudinary upload failed, falling back to local storage:',
            error
          );
          const localResult = await this.uploadLocally(file, folder);
          fileUrl = localResult.url;
        }
      } else {
        const localResult = await this.uploadLocally(file, folder);
        fileUrl = localResult.url;
      }

      return {
        id: uuidv4(),
        fileName: file.originalName,
        fileUrl,
        fileType: file.mimetype,
        fileSize: file.size,
        cloudinaryPublicId,
      };
    } catch (error) {
      logger.error('Error in file upload:', error);
      throw error;
    }
  }

  // Upload multiple files
  static async uploadMultipleFiles(
    files: FileUploadData[],
    folder: string,
    options?: FileValidationOptions & { useCloudinary?: boolean }
  ): Promise<UploadedFile[]> {
    const uploadPromises = files.map((file) =>
      this.uploadFile(file, folder, options)
    );

    try {
      return await Promise.all(uploadPromises);
    } catch (error) {
      logger.error('Error uploading multiple files:', error);
      throw new Error('Failed to upload one or more files');
    }
  }

  // Delete file from Cloudinary
  static async deleteFromCloudinary(publicId: string): Promise<void> {
    try {
      await cloudinary.uploader.destroy(publicId, {
        invalidate: true,
        resource_type: 'auto',
      });
    } catch (error) {
      logger.error('Error deleting from Cloudinary:', error);
      throw new Error('Failed to delete file from cloud storage');
    }
  }

  // Delete file locally
  static async deleteLocally(filePath: string): Promise<void> {
    try {
      const fullPath = path.join(process.cwd(), filePath);
      if (fs.existsSync(fullPath)) {
        fs.unlinkSync(fullPath);
      }
    } catch (error) {
      logger.error('Error deleting local file:', error);
      throw new Error('Failed to delete local file');
    }
  }

  // Delete file (handles both Cloudinary and local)
  static async deleteFile(
    fileUrl: string,
    cloudinaryPublicId?: string
  ): Promise<void> {
    try {
      if (cloudinaryPublicId) {
        await this.deleteFromCloudinary(cloudinaryPublicId);
      } else if (fileUrl.startsWith('/uploads/')) {
        await this.deleteLocally(fileUrl);
      }
    } catch (error) {
      logger.error('Error deleting file:', error);
      // Don't throw error for file deletion failures
    }
  }
}
