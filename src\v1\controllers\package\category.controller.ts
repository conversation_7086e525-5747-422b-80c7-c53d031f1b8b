import { Request, Response, NextFunction } from 'express';
import { controllerOperations } from '../handlers/handleController';
import { categoryService } from '../../services/package/category';

const CreateCategoryHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(categoryService.createCategory, req.body, res, staffId);
};

const ListPackageCategoryHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    categoryService.getAllCategoriesAdmin,
    req.query,
    res,
    staffId
  );
};

const ListPublicCategoryHandler = (req: Request, res: Response) => {
  controllerOperations(categoryService.getAllCategoriesPublic, undefined, res);
};

const SinglePackageCategoryHandler = (req: Request, res: Response) => {
  const { slug } = req.params;
  controllerOperations(categoryService.getCategoryBySlug, req.query, res, slug);
};

const SinglePackageCategoryByIdHandler = (req: Request, res: Response) => {
  const { categoryId } = req.params;
  controllerOperations(
    categoryService.getCategoryById,
    req.query,
    res,
    categoryId
  );
};

const UpdatePackageCategoryHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(categoryService.updateCategory, req.body, res, staffId);
};

const DeletePackageCategoryHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  const categoryId = Number(req.params.categoryId);
  controllerOperations(
    categoryService.deleteCategory,
    categoryId,
    res,
    staffId
  );
};

export const categoryControllers = {
  CreateCategoryHandler,
  ListPackageCategoryHandler,
  SinglePackageCategoryHandler,
  UpdatePackageCategoryHandler,
  DeletePackageCategoryHandler,
  ListPublicCategoryHandler,
  SinglePackageCategoryByIdHandler,
  //   UpdateAdminHandler
};
