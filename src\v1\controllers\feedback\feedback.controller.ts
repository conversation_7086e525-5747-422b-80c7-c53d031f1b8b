import { Request, Response } from 'express';
import { controllerOperations } from '../handlers/handleController';
import { feedbackService } from '../../services/feedback';

/**
 * <PERSON>le creating a new feedback entry
 */
const CreateFeedbackHandler = (req: Request, res: Response) => {
  controllerOperations(
    feedbackService.createFeedback,
    undefined,
    res,
    req.body
  );
};

/**
 * <PERSON>le listing all feedback entries with pagination and filtering
 */
const ListFeedbackHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;

  controllerOperations(feedbackService.getAllFeedback, req.query, res, staffId);
};

const UpdateFeedbackHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(feedbackService.updateFeedback, req.body, res, staffId);
};

export const feedbackControllers = {
  CreateF<PERSON>back<PERSON><PERSON><PERSON>,
  ListFeedback<PERSON><PERSON><PERSON>,
  UpdateFeedbackHandler,
};
