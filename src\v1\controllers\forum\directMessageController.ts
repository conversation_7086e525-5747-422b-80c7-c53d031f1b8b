import { Request, Response } from 'express';
import { DirectMessageService } from '../../services/forum/directMessageService';
import { FileUploadService } from '../../services/upload/fileUploadService';
import { convertMulterFiles } from '../../utils/upload/forumUpload';
import { logger } from '../../utils/logger';

export class DirectMessageController {
  // Send a direct message
  static async sendMessage(req: Request, res: Response) {
    try {
      const { content, messageType, receiverId, attachments } = req.body;
      const senderId = req.Staff as unknown as number;

      if (!senderId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      if (!content || !receiverId) {
        res.status(400).json({
          success: false,
          message: 'Content and receiver ID are required',
        });
      }

      const message = await DirectMessageService.sendDirectMessage({
        content,
        messageType,
        senderId,
        receiverId: parseInt(receiverId),
        attachments,
      });

      res.status(201).json({
        success: true,
        message: 'Direct message sent successfully',
        data: message,
      });
    } catch (error) {
      logger.error('Error sending direct message:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'Failed to send direct message',
      });
    }
  }

  // Send a direct message with file attachments
  static async sendMessageWithFiles(req: Request, res: Response) {
    try {
      const { content, messageType, receiverId } = req.body;
      const senderId = req.Staff as unknown as number;
      const files = req.files as Express.Multer.File[];

      if (!senderId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      if (!content || !receiverId) {
        res.status(400).json({
          success: false,
          message: 'Content and receiver ID are required',
        });
      }

      let attachments = undefined;

      // Handle file uploads if files are provided
      if (files && files.length > 0) {
        try {
          const fileData = convertMulterFiles(files);
          const uploadedFiles = await FileUploadService.uploadMultipleFiles(
            fileData,
            'direct-messages',
            { useCloudinary: true }
          );

          attachments = uploadedFiles.map((file) => ({
            fileName: file.fileName,
            fileUrl: file.fileUrl,
            fileType: file.fileType,
            fileSize: file.fileSize,
          }));
        } catch (uploadError) {
          logger.error('Error uploading files:', uploadError);
          res.status(400).json({
            success: false,
            message:
              uploadError instanceof Error
                ? uploadError.message
                : 'Failed to upload files',
          });
        }
      }

      const message = await DirectMessageService.sendDirectMessage({
        content,
        messageType,
        senderId,
        receiverId: parseInt(receiverId),
        attachments,
      });

      res.status(201).json({
        success: true,
        message: 'Direct message with attachments sent successfully',
        data: message,
      });
    } catch (error) {
      logger.error('Error sending direct message with files:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'Failed to send direct message',
      });
    }
  }

  // Get conversation between two users
  static async getConversation(req: Request, res: Response) {
    try {
      const { userId } = req.params;
      const { page = 1, limit = 50 } = req.query;
      const currentUserId = req.Staff as unknown as number;

      if (!currentUserId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      const result = await DirectMessageService.getConversation(
        currentUserId,
        parseInt(userId),
        parseInt(page as string),
        parseInt(limit as string)
      );

      res.status(200).json({
        success: true,
        data: result.messages,
        pagination: result.pagination,
      });
    } catch (error) {
      logger.error('Error getting conversation:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to get conversation',
      });
    }
  }

  // Get all conversations for current user
  static async getUserConversations(req: Request, res: Response) {
    try {
      const { page = 1, limit = 20 } = req.query;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      const result = await DirectMessageService.getUserConversations(
        userId,
        parseInt(page as string),
        parseInt(limit as string)
      );

      res.status(200).json({
        success: true,
        data: result.conversations,
        pagination: result.pagination,
      });
    } catch (error) {
      logger.error('Error getting user conversations:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'Failed to get user conversations',
      });
    }
  }

  // Mark messages as read
  static async markAsRead(req: Request, res: Response) {
    try {
      const { userId } = req.params;
      const currentUserId = req.Staff as unknown as number;

      if (!currentUserId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      const result = await DirectMessageService.markAsRead(
        parseInt(userId),
        currentUserId
      );

      res.status(200).json({
        success: true,
        message: 'Messages marked as read',
        data: result,
      });
    } catch (error) {
      logger.error('Error marking messages as read:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'Failed to mark messages as read',
      });
    }
  }

  // Get unread message count
  static async getUnreadCount(req: Request, res: Response) {
    try {
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      const result = await DirectMessageService.getUnreadCount(userId);

      res.status(200).json({
        success: true,
        data: result,
      });
    } catch (error) {
      logger.error('Error getting unread count:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to get unread count',
      });
    }
  }

  // Update direct message
  static async updateMessage(req: Request, res: Response) {
    try {
      const { messageId } = req.params;
      const { content } = req.body;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      if (!content) {
        res.status(400).json({
          success: false,
          message: 'Content is required',
        });
      }

      const message = await DirectMessageService.updateMessage(
        messageId,
        { content },
        userId
      );

      res.status(200).json({
        success: true,
        message: 'Direct message updated successfully',
        data: message,
      });
    } catch (error) {
      logger.error('Error updating direct message:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'Failed to update direct message',
      });
    }
  }

  // Delete direct message
  static async deleteMessage(req: Request, res: Response) {
    try {
      const { messageId } = req.params;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      await DirectMessageService.deleteMessage(messageId, userId);

      res.status(200).json({
        success: true,
        message: 'Direct message deleted successfully',
      });
    } catch (error) {
      logger.error('Error deleting direct message:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'Failed to delete direct message',
      });
    }
  }

  // Search direct messages
  static async searchMessages(req: Request, res: Response) {
    try {
      const { query, page = 1, limit = 20 } = req.query;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      if (!query) {
        res.status(400).json({
          success: false,
          message: 'Search query is required',
        });
      }

      const result = await DirectMessageService.searchMessages(
        userId,
        query as string,
        parseInt(page as string),
        parseInt(limit as string)
      );

      res.status(200).json({
        success: true,
        data: result.messages,
        pagination: result.pagination,
        query: result.query,
      });
    } catch (error) {
      logger.error('Error searching direct messages:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'Failed to search direct messages',
      });
    }
  }

  // Get conversation summary
  static async getConversationSummary(req: Request, res: Response) {
    try {
      const { userId } = req.params;
      const currentUserId = req.Staff as unknown as number;

      if (!currentUserId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      // Get latest message and unread count for this conversation
      const [conversation, unreadCount] = await Promise.all([
        DirectMessageService.getConversation(
          currentUserId,
          parseInt(userId),
          1,
          1
        ),
        DirectMessageService.getUnreadCount(currentUserId),
      ]);

      const latestMessage = conversation.messages[0] || null;

      res.status(200).json({
        success: true,
        data: {
          latestMessage,
          unreadCount: unreadCount.count,
          otherUserId: parseInt(userId),
        },
      });
    } catch (error) {
      logger.error('Error getting conversation summary:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'Failed to get conversation summary',
      });
    }
  }

  // Block user (prevent receiving messages)
  static async blockUser(req: Request, res: Response) {
    try {
      const { userId } = req.params;
      const currentUserId = req.Staff as unknown as number;

      if (!currentUserId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      // This would require implementing a blocked users table
      // For now, return a placeholder response
      res.status(200).json({
        success: true,
        message: 'User blocking feature will be implemented',
        data: {
          blockedUserId: parseInt(userId),
          blockedBy: currentUserId,
        },
      });
    } catch (error) {
      logger.error('Error blocking user:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to block user',
      });
    }
  }

  // Unblock user
  static async unblockUser(req: Request, res: Response) {
    try {
      const { userId } = req.params;
      const currentUserId = req.Staff as unknown as number;

      if (!currentUserId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      // This would require implementing a blocked users table
      // For now, return a placeholder response
      res.status(200).json({
        success: true,
        message: 'User unblocking feature will be implemented',
        data: {
          unblockedUserId: parseInt(userId),
          unblockedBy: currentUserId,
        },
      });
    } catch (error) {
      logger.error('Error unblocking user:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to unblock user',
      });
    }
  }
}
