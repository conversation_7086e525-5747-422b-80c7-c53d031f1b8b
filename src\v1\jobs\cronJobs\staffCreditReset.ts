import cron from 'node-cron';
import { logger } from '../../utils/logger';
import { resetStaffMonthlyCreditUsed } from '../../services/staff/creditReset';

/**
 * Cron job to automatically reset all staff's monthlyCreditUsed to 0.0 at the beginning of each month
 * Runs on the 1st day of every month at midnight (00:00)
 * @returns The scheduled task that can be used to stop the job
 */
export const setupStaffCreditResetJob = (): cron.ScheduledTask => {
  // Schedule the job to run at midnight on the 1st day of every month
  // Cron format: second(optional) minute hour day-of-month month day-of-week
  // '0 0 1 * *' means: at 00:00 on day-of-month 1 in every month
  return cron.schedule(
    '0 0 1 * *',
    async () => {
      try {
        logger.info('Running monthly staff credit reset job');

        // Use the shared function to reset all staff monthly credit usage
        const resetCount = await resetStaffMonthlyCreditUsed();

        if (resetCount === 0) {
          logger.info('No staff records found to reset monthly credit usage');
        } else {
          logger.info(
            `Successfully reset monthly credit usage for ${resetCount} staff members`
          );
        }
      } catch (error) {
        logger.error('Error in monthly staff credit reset job:', error);
      }
    },
    {
      scheduled: true,
      timezone: 'UTC', // You can adjust this to your local timezone if needed
    }
  );
};

logger.info('Monthly staff credit reset job scheduled');
