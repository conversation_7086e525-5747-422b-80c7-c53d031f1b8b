import { db } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import { staffHasPermission, PERMISSIONS } from '../../utils/permission';
import { createDateFilter } from '../../utils/util';
import { logger } from '../../utils/logger';

export interface CreateInnovationIdeaData {
  title: string;
  description: string;
  status?: 'DRAFT' | 'PENDING_REVIEW';
}

export interface UpdateInnovationIdeaData {
  title?: string;
  description?: string;
  ideaId: number;
  status?: 'DRAFT' | 'PENDING_REVIEW' | 'ACCEPTED' | 'REJECTED' | 'IMPLEMENTED';
}

export const innovationIdeaService = {
  createInnovationIdea: async (
    staffId: number,
    reqBody: CreateInnovationIdeaData
  ) => {
    try {
    await staffHasPermission(staffId, PERMISSIONS.HUB_CREATE);
      const { title, description } = reqBody;

      if (!title || !description) {
        throw new HttpError('Title and description are required', 400);
      }

      await db.innovationIdea.create({
        data: {
          title,
          description,
          status: 'DRAFT',
          authorId: staffId,
        },
      });

      return {
        message: 'Innovation idea submitted successfully',
      };
    } catch (error) {
      logger.error('Error submitting innovation idea:', error);
      throw new HttpError('Failed to create innovation idea', 400);
    }
  },

  // Get all innovation ideas with pagination and filtering
  getAllInnovationIdeas: async (staffId: number, query: any) => {
    try {
      const {
        page,
        limit,
        status,
        search,
        startDate,
        endDate,
        sortBy = 'createdAt',
        sortOrder = 'desc',
      } = query;

      const skip = (parseInt(page) - 1) * parseInt(limit);
      const take = parseInt(limit);

const whereClause: any = {
  ...(status ? { status } : {}),
  ...(search
    ? {
        OR: [
          { title: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
          { author: { fullName: { contains: search, mode: 'insensitive' } } },
        ],
      }
    : {}),
  ...((startDate || endDate) ? { createdAt: createDateFilter(startDate, endDate) } : {}),
};

      const [ideas, total] = await Promise.all([
        db.innovationIdea.findMany({
          where: whereClause,
          skip,
          take,
          orderBy: {
            [sortBy]: sortOrder,
          },
          include: {
            author: {
              select: {
                id: true,
                fullName: true,
                email: true,
                department: {
                  select: {
                    name: true,
                  },
                },
              },
            },
            comments: {
              select: {
                content: true,
                createdAt: true,
                staff: {
                  select: {
                    fullName: true,
                  },
                },
              }
            },
            likes: {
              select: {
                staff: {
                  select: {
                    fullName: true,
                  },
                },
              },
            }
          },
        }),
        db.innovationIdea.count({ where: whereClause }),
      ]);

      return {
        ideas,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(total / take),
          totalItems: total,
          itemsPerPage: take,
        },
      };
    } catch (error) {
      logger.error('Error getting innovation ideas:', error);
      throw new HttpError('Failed to fetch innovation ideas', 400);
    }
  },

  // Update an innovation idea
  updateInnovationIdea: async (
    staffId: number,
    reqBody: UpdateInnovationIdeaData
  ) => {
    try {
    await staffHasPermission(staffId, PERMISSIONS.HUB_EDIT);
      const idea = await db.innovationIdea.findUnique({
        where: { id: Number(reqBody.ideaId) },
      });

      if (!idea) {
        throw new HttpError('Innovation idea not found', 404);
      }

      const updateData: any = {};

      if (reqBody.title) {
        updateData.title = reqBody.title.trim();
      }

      if (reqBody.description) {
        updateData.description = reqBody.description.trim();
      }

      if (reqBody.status) {
        updateData.status = reqBody.status;

        // Set appropriate timestamps based on status
        if (reqBody.status === 'PENDING_REVIEW' && !idea.reviewedAt) {
          updateData.reviewedAt = new Date();
        } else if (reqBody.status === 'ACCEPTED' && !idea.acceptedAt) {
          updateData.acceptedAt = new Date();
        } else if (reqBody.status === 'REJECTED' && !idea.rejectedAt) {
          updateData.rejectedAt = new Date();
        } else if (reqBody.status === 'IMPLEMENTED' && !idea.implementedAt) {
          updateData.implementedAt = new Date();
        }
      }

      const updatedIdea = await db.innovationIdea.update({
        where: { id: Number(reqBody.ideaId) },
        data: updateData,
      });

      return {
        message: 'Innovation idea updated successfully',
        data: updatedIdea,
      };
    } catch (error) {
      logger.error('Error updating innovation idea:', error);
      throw new HttpError('Failed to update innovation idea', 400);
    }
  },

  // Delete an innovation idea
  deleteInnovationIdea: async (staffId: number, ideaId: number) => {
    try {
    await staffHasPermission(staffId, PERMISSIONS.HUB_EDIT);
      const idea = await db.innovationIdea.findUnique({
        where: { id: ideaId },
      });

      if (!idea) {
        throw new HttpError('Innovation idea not found', 404);
      }

      // Check if user is the author or has admin permissions
      if (idea.authorId !== staffId) {
        await staffHasPermission(staffId, PERMISSIONS.STAFF_DELETE);
      }

      await db.innovationIdea.delete({
        where: { id: ideaId },
      });

      return {
        message: 'Innovation idea deleted successfully',
      };
    } catch (error) {
      logger.error('Error deleting innovation idea:', error);
      throw new HttpError('Failed to delete innovation idea', 400);
    }
  },
};
