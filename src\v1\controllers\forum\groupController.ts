import { Request, Response } from 'express';
import { ForumGroupService } from '../../services/forum/groupService';
import { logger } from '../../utils/logger';

export class ForumGroupController {
  // Create a new forum group
  static async createGroup(req: Request, res: Response) {
    try {
      const { name, description, isPrivate, locationId } = req.body;
      const createdById = req.Staff as unknown as number;

      if (!createdById) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      if (!name) {
        res.status(400).json({
          success: false,
          message: 'Group name is required',
        });
      }

      const group = await ForumGroupService.createGroup({
        name,
        description,
        isPrivate: isPrivate || false,
        createdById,
        locationId,
      });

      res.status(201).json({
        success: true,
        message: 'Forum group created successfully',
        data: group,
      });
    } catch (error) {
      logger.error('Error creating forum group:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'Failed to create forum group',
      });
    }
  }

  // Get group by ID
  static async getGroup(req: Request, res: Response) {
    try {
      const { groupId } = req.params;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      const group = await ForumGroupService.getGroupById(
        parseInt(groupId),
        userId
      );

      res.status(200).json({
        success: true,
        data: group,
      });
    } catch (error) {
      logger.error('Error getting forum group:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to get forum group',
      });
    }
  }

  // Get user's groups
  static async getUserGroups(req: Request, res: Response) {
    try {
      const userId = req.Staff as unknown as number;
      const { page = 1, limit = 20 } = req.query;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      const result = await ForumGroupService.getUserGroups(
        userId,
        parseInt(page as string),
        parseInt(limit as string)
      );

      res.status(200).json({
        success: true,
        data: result.groups,
        pagination: result.pagination,
      });
    } catch (error) {
      logger.error('Error getting user groups:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to get user groups',
      });
    }
  }

  // Update group
  static async updateGroup(req: Request, res: Response) {
    try {
      const { groupId } = req.params;
      const { name, description, isPrivate } = req.body;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      const group = await ForumGroupService.updateGroup(
        parseInt(groupId),
        { name, description, isPrivate },
        userId
      );

      res.status(200).json({
        success: true,
        message: 'Group updated successfully',
        data: group,
      });
    } catch (error) {
      logger.error('Error updating forum group:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'Failed to update forum group',
      });
    }
  }

  // Add member to group
  static async addMember(req: Request, res: Response) {
    try {
      const { groupId } = req.params;
      const { staffId, role = 'MEMBER' } = req.body;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      if (!staffId) {
        res.status(400).json({
          success: false,
          message: 'Staff ID is required',
        });
      }

      const member = await ForumGroupService.addMember({
        groupId: parseInt(groupId),
        staffId,
        role,
      });

      res.status(201).json({
        success: true,
        message: 'Member added successfully',
        data: member,
      });
    } catch (error) {
      logger.error('Error adding group member:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to add group member',
      });
    }
  }

  // Remove member from group
  static async removeMember(req: Request, res: Response) {
    try {
      const { groupId, memberId } = req.params;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      await ForumGroupService.removeMember(
        parseInt(groupId),
        parseInt(memberId),
        userId
      );

      res.status(200).json({
        success: true,
        message: 'Member removed successfully',
      });
    } catch (error) {
      logger.error('Error removing group member:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'Failed to remove group member',
      });
    }
  }

  // Get group members
  static async getGroupMembers(req: Request, res: Response) {
    try {
      const { groupId } = req.params;
      const { page = 1, limit = 50 } = req.query;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      // First check if user has access to the group
      await ForumGroupService.getGroupById(parseInt(groupId), userId);

      const members = await ForumGroupService.getGroupMembers(
        parseInt(groupId),
        parseInt(page as string),
        parseInt(limit as string)
      );

      res.status(200).json({
        success: true,
        data: members.members,
        pagination: members.pagination,
      });
    } catch (error) {
      logger.error('Error getting group members:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'Failed to get group members',
      });
    }
  }

  // Update member role
  static async updateMemberRole(req: Request, res: Response) {
    try {
      const { groupId, memberId } = req.params;
      const { role } = req.body;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      if (!role) {
        res.status(400).json({
          success: false,
          message: 'Role is required',
        });
      }

      const member = await ForumGroupService.updateMemberRole(
        parseInt(groupId),
        parseInt(memberId),
        role,
        userId
      );

      res.status(200).json({
        success: true,
        message: 'Member role updated successfully',
        data: member,
      });
    } catch (error) {
      logger.error('Error updating member role:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'Failed to update member role',
      });
    }
  }

  // Delete group
  static async deleteGroup(req: Request, res: Response) {
    try {
      const { groupId } = req.params;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      await ForumGroupService.deleteGroup(parseInt(groupId), userId);

      res.status(200).json({
        success: true,
        message: 'Group deleted successfully',
      });
    } catch (error) {
      logger.error('Error deleting forum group:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'Failed to delete forum group',
      });
    }
  }

  // Search groups
  static async searchGroups(req: Request, res: Response) {
    try {
      const { query, page = 1, limit = 20 } = req.query;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      if (!query) {
        res.status(400).json({
          success: false,
          message: 'Search query is required',
        });
      }

      const result = await ForumGroupService.searchGroups(
        query as string,
        userId,
        parseInt(page as string),
        parseInt(limit as string)
      );

      res.status(200).json({
        success: true,
        data: result.groups,
        pagination: result.pagination,
        query,
      });
    } catch (error) {
      logger.error('Error searching groups:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to search groups',
      });
    }
  }
}
